<template>
  <div class="wrapper" id="wrapper">
    <TitleBar></TitleBar>
    <Map class='map'></Map>
    <div class='close_left_right_iframe_btn' @click='showPage'></div>
    <router-view />
  </div>
</template>

<script>
import TitleBar from '@/components/TitleBar'
import Map from '@/components/Map/index.vue'
export default {
  name: 'MainLayout',
  components: {
    TitleBar,
    Map
  },
  data() {
    return {
      showFlag: true
    }
  },
  methods: {
    showPage() {
      this.showFlag = !this.showFlag
      this.$bus.$emit("showPage", !this.showFlag)
    }
  }
}
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-size: cover;
}
.map {
  position: fixed;
  top: 0;
  z-index: 1;
}

/* 展开和收起iframe */
.close_left_right_iframe_btn {
  width: 74px;
  height: 74px;
  background-image: url('@/assets/index/oepn_iframe.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  z-index: 778;
  right: 20px;
  top: 130px;
  cursor: pointer;
}
</style>
