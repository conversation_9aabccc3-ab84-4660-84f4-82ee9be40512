<!--执法仪弹窗非国标-->
<template>
  <ygfDialog :visible='visible' width='1500px' style='margin-top: 240px;'>
    <div class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 font-syBold dialogTitle">{{ title }}</div>
        <div class="close" @click="closeVideo"></div>
      </div>
      <video
        ref="video"
        controls="controls"
        autoplay="autoplay"
        muted="false"
        style="
          width: 94%;
          height: 90%;
          position: absolute;
          top: 75px;
          left: 50px;
        "
      ></video>
      <div
        class="control-panel"
        style="
          z-index: 1000;
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 10px;
          border-radius: 5px;
          gap: 10px;
        "
      >
        <button
          class="btn"
          style="
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          "
          @click="startVideo"
        >
          播放视频
        </button>
        <button
          class="btn"
          style="
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          "
          @click="toggleAudio"
        >
          {{ isAudio ? '关闭对讲' : '开启对讲' }}
        </button>
        <button
          class="btn"
          style="
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          "
          @click="stopAll"
        >
          停止
        </button>
      </div>
      <div
        class="status"
        ref="statusText"
        style="
          position: absolute;
          top: 60px;
          left: 20px;
          color: #fff;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 5px 10px;
          border-radius: 3px;
          display: none;
        "
      ></div>
      <div
        class="loading"
        ref="loading"
        style="
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: 18px;
          display: none;
        "
      >
        正在加载，请稍候...
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { hex_md5 } from './md5.js'
import Webrtc from './webrtc.js'

export default {
  name: "index",
  components: {
    ygfDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    baseURL: {
      type: String,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    pwd: {
      type: String,
      required: true
    },
    videoCode: {
      type: String,
      default: "T0C2675"
    },
    title: {
      type: String,
      default: "执法记录仪"
    },
    type: {
      type: String,
      default: "openVideoTest" // 可选值: openVideoTest, ddhjVideoTest
    }
  },
  data() {
    return {
      player: null,
      isAudio: false,
      recorders: null,
      recordersPlatform: null,
      ws: null,
      timerId: null,
      dataInfo_set: [],
      form: {
        hostbodyArr: null,
        hostbody: null
      },
      dataInfo_hostbody: null,
      zfjlyApi: null,
      wsConnected: false,
      webRtcUrl: null,
      currentSn: null,
      currentHostbody: null
    };
  },
  created() {
    this.initZfjlyApi();
  },
  mounted() {
    if (this.visible) {
      this.init();
    }
  },
  beforeDestroy() {
    this.closeAll();
  },
  methods: {
    async init() {
      this.showLoading(true);
      this.showStatus("正在连接执法记录仪平台...");

      this.currentHostbody = this.videoCode;

      // 登录系统
      const loginSuccess = await this.loginZFJLY();
      if (loginSuccess) {
        this.showStatus("已连接执法记录仪平台");
        this.wsConnected = true;

        // 获取设备信息
        await this.getDeviceInfo();
        this.showLoading(false);

        if (this.type === "openVideoTest") {
          this.startVideo();
        } else if (this.type === "ddhjVideoTest") {
          this.startVideo();
          setTimeout(() => {
            this.startAudio();
          }, 2000);
        }
      } else {
        this.showStatus("连接执法记录仪平台失败");
        this.showLoading(false);
      }
    },

    // 显示/隐藏加载状态
    showLoading(show) {
      this.$refs.loading.style.display = show ? "flex" : "none";
    },

    // 显示状态文本
    showStatus(text) {
      this.$refs.statusText.textContent = text;
      this.$refs.statusText.style.display = text ? "block" : "none";
      console.log("状态:", text);
    },

    initZfjlyApi() {
      // 创建axios实例
      const axios = require('axios');
      this.zfjlyApi = {
        get: axios.create({
          baseURL: this.baseURL,
          withCredentials: true
        }).get,
        post: axios.create({
          baseURL: this.baseURL,
          withCredentials: true
        }).post,

        // 构建请求参数
        param_up(param_arr) {
          var keys = Object.keys(param_arr).sort();
          var string = "";
          for (var i = 0; i < keys.length; i++) {
            var k = keys[i];
            string += k + "=" + param_arr[k] + ";";
          }
          string += hex_md5("Pe2695jingyi");
          let str_encode = encodeURIComponent(string);
          //编码后MD5加密
          param_arr.pe_signals = hex_md5(str_encode);
          return JSON.stringify(param_arr);
        },

        // 扩展信号
        extendSignal(target) {
          let keys = Object.keys(target),
            arr = [],
            solt = "Pe2695jingyi",
            str,
            pe_signals;
          keys.sort(); // 排序
          keys.forEach((key) => {
            const value = JSON.stringify(target[key]);
            arr.push(`${key}=${value}`);
          });
          str = arr.join(";") + hex_md5(solt);
          str = encodeURIComponent(str);
          pe_signals = hex_md5(str);
          target.pe_signals = pe_signals;
          return target;
        }
      };
    },
    closeVideo() {
      // 停止所有流和释放资源
      this.stopAll();
      this.$emit('close');
    },
    // 登录执法记录仪平台
    async loginZFJLY() {
      try {
        const res = await this.zfjlyApi.get(`rest/index/login/get?key=""`);

        const send = {
          username: this.username,
          pwd: this.pwd,
          token: res.data
        };

        const el = await this.login(send);

        if (el.data.code === 200) {
          await this.heartbeat();
          const e = await this.getUserInformation();

          try {
            this.ws = new WebSocket(e.data.data.wsurl);
          } catch (error) {
            console.log("WebSocket连接错误:", error);
            return false;
          }

          const data1 = {
            logincode: e.data.data.logincode,
            username: e.data.data.username,
            scode: e.data.data.scode,
            cate: e.data.data.auth_cate
          };

          const psd = {
            command: "client_login",
            data: JSON.stringify(data1)
          };

          return new Promise((resolve) => {
            this.ws.onopen = () => {
              this.ws.send(JSON.stringify(psd));
              resolve(true);
            };

            this.ws.onerror = (e) => {
              console.warn("socket连接出错", e);
              this.ws.close();
              this.ws = null;
              resolve(false);
            };

            this.ws.onclose = () => {
              console.log("ws已断开连接");
            };

            this.ws.onmessage = (event) => {
              console.log("收到消息:", event.data);
              try {
                const data = JSON.parse(event.data);

                // 处理site_data信息
                if (data.site_data) {
                  const dataInfo = data.site_data;

                  // 处理开始播放视频事件
                  if (dataInfo.start_live) {
                    if (dataInfo.start_live.recorder_type == "2") {
                      this.isC2 = true;
                    } else {
                      this.isC2 = false;
                    }
                    const ws = "ws:/" + dataInfo.start_live.wsInfo.wsIp + ":" + dataInfo.start_live.wsInfo.wsPort;
                    const viewId = dataInfo.start_live.wsInfo.wsViewId;
                    this.pullFlow_vms2(ws, viewId);
                  }

                  // 处理开始音频事件
                  if (dataInfo.start_audio) {
                    this.isAudio = true;
                    const wss = `ws://${dataInfo.start_audio.wsInfo.wsIp}:${dataInfo.start_audio.wsInfo.wsPort}`;
                    this.wsChannelId = dataInfo.start_audio.wsInfo.wsChannelId;
                    this.currentSn = dataInfo.start_audio.wsInfo.sn;
                    this.voice_pull_vms2(wss, this.wsChannelId);
                  }
                }
              } catch (error) {
                console.error("解析WebSocket消息错误:", error);
              }
            };

            // 5秒后超时处理
            setTimeout(() => {
              if (!this.wsConnected) {
                resolve(false);
              }
            }, 5000);
          });
        }
      } catch (error) {
        console.error("登录执法记录仪平台失败:", error);
        return false;
      }
      return false;
    },
    login(param) {
      let { username, pwd, token, captcha_code } = param;
      let password = hex_md5(pwd);

      let encodedData = encodeURIComponent(JSON.stringify({username, password}));
      let str = this.toBase64(encodedData)
      return this.zfjlyApi.post('/rest/index/login/login', {login_info: str,token,captcha_code,withCredentials: true});
    },
    toBase64(input) {
      var _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
      var output = "";
      var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
      var i = 0;
      input = this._utf8_encode(input);
      while (i < input.length) {
        chr1 = input.charCodeAt(i++);
        chr2 = input.charCodeAt(i++);
        chr3 = input.charCodeAt(i++);
        enc1 = chr1 >> 2;
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        enc4 = chr3 & 63;
        if (isNaN(chr2)) {
          enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
          enc4 = 64;
        }
        output = output +
          _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
          _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
      }
      return output;
    },
    _utf8_encode(string) {
      string = string.replace(/\r\n/g, "\n");
      var utftext = "";
      for (var n = 0; n < string.length; n++) {
        var c = string.charCodeAt(n);
        if (c < 128) {
          utftext += String.fromCharCode(c);
        } else if ((c > 127) && (c < 2048)) {
          utftext += String.fromCharCode((c >> 6) | 192);
          utftext += String.fromCharCode((c & 63) | 128);
        } else {
          utftext += String.fromCharCode((c >> 12) | 224);
          utftext += String.fromCharCode(((c >> 6) & 63) | 128);
          utftext += String.fromCharCode((c & 63) | 128);
        }
      }
      return utftext;
    },
    getUserInformation() {
      return this.zfjlyApi.get('/rest/user/user/get_info');
    },
    // 心跳
    heartbeat() {
      return new Promise((resolve) => {
        this.heart();
        this.timerId = setInterval(this.heart, 20000);
        resolve();
      });
    },
    heart() {
      this.online().then((e) => {
        console.log("心跳请求成功");
      });
    },
    online() {
      return this.zfjlyApi.get('rest/other/user/online');
    },
    // 获取设备信息
    async getDeviceInfo() {
      try {
        const res = await this.unitEquipTree ("1001", "bh", "dname", false);
        var lineon = [];
        let info = {
          ids: []
        };

        res.data.data.forEach((item) => {
          if (item.lineon == 1) {
            this.dataInfo_set.push(item);
            lineon.push(item.hostbody);
            info.ids.push(item.hostbody);
          }
        });

        const posRes = await this.getPosition(info);
        let data = posRes.data.data;
        if (data && data.length > 0) {
          data.forEach((item) => {
            let lat = item.lat;
            let lng = item.lng;
            let name = item.name;
          });
        }

        this.dataInfo_hostbody = lineon.toString();
        this.form.hostbodyArr = this.dataInfo_hostbody;
        this.form.hostbody = this.videoCode;
        return this.dataInfo_set;
      } catch (error) {
        console.error("获取设备信息失败:", error);
        return [];
      }
    },
    unitEquipTree(id = '', bh = 'bh', text = 'dname', isNewapi = false) {
      let data = {
        "id": id,
        "bh": bh,
        "text": text
      };
      this.zfjlyApi.extendSignal(data);

      if (isNewapi) {
        return this.zfjlyApi.post('/rest/other/unitjson/gdlist_dv', data);
      } else {
        return this.zfjlyApi.post('/rest/other/unitjson/gdlist', data);
      }
    },
    // 获取设备经纬度信息
    getPosition(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/gis/gismoni/get_point', data);
    },

    // 开始视频播放
    async startVideo() {
      if (!this.currentHostbody || !this.wsConnected) {
        this.showStatus("请先选择设备或确保连接正常");
        return;
      }

      this.showLoading(true);
      this.showStatus("正在连接视频流...");

      try {
        const send = {
          hostbody_arr: [this.currentHostbody]
        };

        const res = await this.startLiveVideo(send);

        if (res.data.code == 200) {
          if (res.data.data[0].is_existed) {
            this.webRtcUrl = res.data.data[0].play_info.webrtc_url;

            // 保存sn值，用于停止视频流时使用
            if (res.data.data[0].sn) {
              this.currentSn = res.data.data[0].sn;
              console.log("获取到视频流SN:", this.currentSn);
            }

            this.showStatus("视频流连接成功，正在加载...");

            // 使用WebRTC方式播放
            const result = await this.createWebRTCPlayer(this.$refs.video, this.webRtcUrl);

            this.showLoading(false);

            if (result && result.type === true) {
              this.showStatus("视频播放中");
              this.$refs.video.classList.add("active");

              // 保存播放器引用
              this.player = result.player;
            } else {
              this.showStatus("视频播放失败");
            }
          } else {
            this.showStatus("设备不在线");
            this.showLoading(false);
          }
        } else {
          this.showStatus("无法连接设备: " + (res.data.msg || "未知错误"));
          this.showLoading(false);
        }
      } catch (err) {
        console.error("视频播放错误:", err);
        this.showStatus("视频播放错误: " + err);
        this.showLoading(false);
      }
    },

    // 切换音频对讲
    toggleAudio() {
      if (this.isAudio) {
        this.stopAudio();
      } else {
        this.startAudio();
      }
    },

    // 开始音频对讲
    async startAudio() {
      if (!this.currentHostbody || !this.wsConnected) {
        this.showStatus("请先选择设备或确保连接正常");
        return;
      }

      this.showLoading(true);
      this.showStatus("正在初始化麦克风...");

      try {
        // 初始化麦克风
        await this.initMicrophone();

        this.showStatus("正在建立对讲连接...");

        // 发送对讲请求
        const send = {
          hostbody_arr: [this.currentHostbody]
        };

        const res = await this.startLiveAudio(send);

        if (res.data.code === 200) {
          const wsBroadcast = `ws://${res.data.data[0].wsip}:${res.data.data[0].wsport}/RTSP/AAC/Broadcast`;
          const logincode = res.data.data[0].logincode;

          this.isAudio = true;

          // 开启对讲
          await this.recordersPlatform.openWebSocket(wsBroadcast, logincode);

          this.showStatus("对讲已开启");
          this.showLoading(false);
        } else {
          this.showStatus("设备不在线或不支持对讲");
          this.showLoading(false);
        }
      } catch (err) {
        console.error("对讲初始化失败:", err);
        this.showStatus("对讲初始化失败");
        this.showLoading(false);
      }
    },

    // 停止音频对讲
    stopAudio() {
      if (!this.isAudio) return;

      this.showStatus("正在关闭对讲...");

      if (this.recordersPlatform) {
        this.recordersPlatform.closeWebsocket();
      }

      this.isAudio = false;
      this.showStatus("对讲已关闭");
    },

    // 初始化麦克风
    initMicrophone() {
      return new Promise((resolve, reject) => {
        // 导入音频处理模块
        import('./JY-chromePlayer.min.js').then(({ HZRecorder_pcm_push, HZRecorder_pcm }) => {
          navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia;

          if (!navigator.getUserMedia) {
            this.$message({
              message: "浏览器不支持音频输入",
              type: "warning"
            });
            reject("浏览器不支持音频输入");
            return;
          }

          navigator.getUserMedia(
            { audio: true },
            (stream) => {
              this.recordersPlatform = new HZRecorder_pcm_push(stream, {});
              this.recorders = new HZRecorder_pcm(stream, {});
              resolve();
            },
            (error) => {
              console.error("麦克风访问失败:", error);
              reject("麦克风访问失败: " + (error.name || error.message || "未知错误"));
            }
          );
        }).catch(err => {
          reject(err);
        });
      });
    },

    // 停止所有流
    stopAll() {
      this.showStatus("正在停止...");

      // 停止音频
      if (this.isAudio) {
        this.stopAudio();
      }

      // 停止视频流
      if (this.currentHostbody && this.currentSn) {
        // 调用接口停止视频流
        const stopData = {
          hostbody_arr: [this.currentHostbody],
          sn_arr: [this.currentSn]
        };

        console.log("调用stopLive接口停止视频流:", stopData);

        this.stopLive(stopData).then(res => {
          if (res.data && res.data.code == 200) {
            console.log("成功停止视频流");
          } else {
            console.warn("停止视频流失败:", res.data);
          }
        }).catch(err => {
          console.error("停止视频流接口调用失败:", err);
        });

        // 重置sn值
        this.currentSn = null;
      }

      // 停止视频播放器
      if (this.player) {
        // 使用WebRTC播放器的正确关闭方法
        if (typeof this.player.close_play === 'function') {
          this.player.close_play(this.$refs.video).then(() => {
            console.log("视频流已关闭");
          }).catch(err => {
            console.error("关闭视频流时出错:", err);
          });
        } else if (typeof this.player.close === 'function') {
          this.player.close();
          console.log("视频流已关闭");
        } else {
          console.warn("播放器没有有效的关闭方法");
          // 尝试直接清理视频元素
          if (this.$refs.video) {
            this.$refs.video.pause();
            this.$refs.video.srcObject = null;
            this.$refs.video.load();
          }
        }
        this.player = null;
      }

      this.showStatus("已停止");
    },

    // 清理所有资源
    closeAll() {
      if (this.player) {
        this.player = null;
      }

      if (this.recorders) {
        this.recorders.closeWebsocket();
        this.recorders = null;
      }

      if (this.recordersPlatform) {
        this.recordersPlatform.closeWebsocket();
        this.recordersPlatform = null;
      }

      if (this.timerId) {
        clearInterval(this.timerId);
        this.timerId = null;
      }

      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
    },

    startLiveVideo(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/startLive', data);
    },

    startLiveAudio(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/startAudio', data);
    },

    stopLive(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/stopLive', data);
    },

    stopLiveAudio(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/stopAudio', data);
    },

    send_cmd(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/gis/gismoni/send_cmd', data);
    },

    // 创建WebRTC播放器并播放视频流
    createWebRTCPlayer(videoElement, webRtcUrl) {
      if (!videoElement) {
        console.error("播放器元素不存在");
        return Promise.reject("播放器元素不存在");
      }

      return new Promise((resolve, reject) => {
        try {
          // 创建WebRTC播放器配置
          const webrtcConfig = {
            element: videoElement,
            debug: true,
            zlmsdpUrl: webRtcUrl,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: true,
            recvOnly: true,
            resolution: { w: 1280, h: 720 },
            usedatachannel: false
          };

          // 创建WebRTC播放器实例
          const webrtcPlayer = new Webrtc(webrtcConfig);

          // 开始播放
          webrtcPlayer.start_play().then(stream => {
            console.log("WebRTC播放成功:", stream);
            resolve({
              player: webrtcPlayer,
              stream: stream,
              type: true
            });
          }).catch(error => {
            console.error("WebRTC播放失败:", error);
            reject(error);
          });
        } catch (error) {
          console.error("创建WebRTC播放器失败:", error);
          reject(error);
        }
      });
    },

    // 拉流处理函数
    pullFlow_vms2(ws, viewId) {
      console.log("pullFlow_vms2", ws, viewId);

      // 使用WebRTC播放
      this.createWebRTCPlayer(this.$refs.video, this.webRtcUrl).then((result) => {
        console.log("WebRTC拉流结果：", result);

        if (!result) {
          console.warn("WebRTC播放失败，可能连接异常");
          this.showStatus("拉流连接异常");
          return;
        }

        if (result.type == true) {
          this.$refs.video.classList.add("active");
          this.player = result.player;

          // 保存来自WebSocket的sn值，如果存在的话
          if (this.currentSn) {
            console.log("WebSocket消息触发的视频流，保存SN:", this.currentSn);
          }

          this.showStatus("视频播放中");
        } else {
          this.showStatus("拉流失败");
        }
      }).catch(error => {
        console.error("WebRTC拉流过程中发生错误：", error);
        this.showStatus("拉流失败，请检查连接");
      });
    },

    // 音频流处理
    voice_pull_vms2(wss, wsChannelId) {
      console.log("voice_pull_vms2", wss, wsChannelId);
      if (this.recorders) {
        this.recorders.openWebSocket(wss, wsChannelId);
        this.isAudio = true;
        this.showStatus("对讲已开启");
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      } else {
        this.closeAll();
      }
    }
  }
};
</script>

<style scoped>
.rwgz-tc {
  position: relative;
  width: 1500px;
  height: 1000px;
  background-image: url("@/assets/zhdd/bg_panel.png");
  background-size: 100% 100%;
  border-radius: 57px;
}

.rw-title {
  position: absolute;
  top: 50px;
  z-index: 888;
  border-radius: 57px 57px 0 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 1% 3%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
  cursor: pointer;
}

.btn:hover {
  background-color: #40a9ff !important;
}

.btn:disabled {
  background-color: #d9d9d9 !important;
  cursor: not-allowed;
}
</style>