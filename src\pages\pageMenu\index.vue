<template>
  <div class="choosePage" id="choosePage">
    <!-- 顶部标题 -->
    <div class="name">
      {{ city + '城市运行管理服务系统' }}
    </div>

    <div class="tops">
      <div
        class="topItem"
        v-for="(item, i) in topArr"
        :key="i"
        @click="pageJump(item)"
        :style="{ background: 'url(' + item.back + ')' }"
      ></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧行政执法指挥中心 -->
      <div class="left-section">
        <div class="section-icon">
          <img src="@/assets/images/pageMenu/page_left_icon.png" alt="行政执法指挥中心" />
        </div>
        <div class="section-title">
          行政执法
          <br />
          指挥中心
        </div>
        <div class="left-menu">
          <div class="menu-item" v-for="(item, i) in leftMenuItems" :key="i" @click="pageJump(item)">
            {{ item.name }}
          </div>
        </div>
      </div>

      <!-- 右侧城市运行管理服务 -->
      <div class="right-section">
        <div class="section-icon">
          <img src="@/assets/images/pageMenu/page_right_icon.png" alt="城市运行管理服务" />
        </div>
        <div class="section-title">
          城市运行
          <br />
          管理服务
        </div>
        <div class="right-menu">
          <div class="menu-item" v-for="(item, i) in rightMenuItems" :key="i" @click="pageJump(item)">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrl, indexApi, getSzcjToken, getUserArea, loginByThirdComplete } from '@/api/indexApi'
import mainObj from '@/utils/PocMain.js'
import { Encrypt } from '@/utils/Encrypt'

export default {
  name: 'index',
  data() {
    return {
      topArr: [
        {
          back: require('@/assets/login/login_admin.png'),
          name: '后台管理',
        },
      ],
      // 左侧菜单项目
      leftMenuItems: [
        {
          name: '行政执法一张图',
          key: 'home',
        },
        {
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          name: '案件回访',
          key: 'ajhf',
        },
        {
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      // 右侧菜单项目
      rightMenuItems: [
        {
          name: '城市运行管理服务首页',
          key: 'csyxgl',
        },
        {
          name: '城市管理一张图',
          key: 'csgl',
        },
        {
          name: '物联网设备一张图',
          key: 'wlwsb',
        },
        {
          name: '垃圾分类一张图',
          key: 'ljfl',
        },
        {
          name: '公众服务一张图',
          key: 'gzfw',
        },
        {
          name: '综合评价一张图',
          key: 'zhpj',
        },
        {
          name: '运行监测一张图',
          key: 'yxjc',
        },
        {
          name: '犬类管理一张图',
          key: 'dog',
        },
        {
          name: '站前执法一张图',
          key: 'zqyzt',
        },
        {
          name: '数字城建一张图',
          key: 'szcj',
        },
        {
          name: '文类管理一张图',
          key: 'dog',
        },
        {
          name: '行业应用集成',
          key: 'yyjc',
        },
      ],
      pageArr: [
        {
          img: require('@/assets/login/login_ssyj.png'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          img: require('@/assets/login/login_xxzf_zhpj.png'),
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          img: require('@/assets/login/login_csgl.png'),
          name: '城市管理一张图',
          key: 'csgl',
        },
        {
          img: require('@/assets/login/login_wlwsb.png'),
          name: '物联网设备一张图',
          key: 'wlwsb',
        },
        {
          img: require('@/assets/login/login_ljfl.png'),
          name: '垃圾分类一张图',
          key: 'ljfl',
        },
        {
          img: require('@/assets/login/login_gzfw.png'),
          name: '公众服务一张图',
          key: 'gzfw',
        },
        {
          img: require('@/assets/login/login_ajhf.png'),
          name: '案件回访',
          key: 'ajhf',
        },
        {
          img: require('@/assets/login/login_yyjc.png'),
          name: '应用集成',
          key: 'yyjc',
        },
        // {
        //   img: require('@/assets/login/login_jxpg.png'),
        //   name: '绩效评估',
        //   key: 'jxpg',
        // },
        {
          img: require('@/assets/login/login_zhpj.png'),
          name: '综合评价一张图',
          key: 'zhpj',
        },
        {
          img: require('@/assets/login/login_yxjc.png'),
          name: '运行监测一张图',
          key: 'yxjc',
        },
        {
          img: require('@/assets/login/login_qlgl.png'),
          name: '犬类管理一张图',
          key: 'dog',
        },
        {
          img: require('@/assets/login/login_zqzf.png'),
          name: '站前执法一张图',
          key: 'zqyzt',
        },
        {
          img: require('@/assets/login/login_szcj.png'),
          name: '数字城建一张图',
          key: 'szcj',
        },
        {
          img: require('@/assets/login/login_xzzf.png'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          img: require('@/assets/login/login_zfts.png'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          img: require('@/assets/login/login_zhdd.png'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          img: require('@/assets/login/login_xjzx.png'),
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      szcjToken: '',
    }
  },
  computed: {
    city() {
      return localStorage.getItem('adminCity')
    },
  },
  mounted() {
    // 初始化 POC

    try {
      mainObj.init()

      // 然后初始化视频数据
      this.initPocVideoData()
    } catch (error) {
      console.error('POC 初始化失败:', error)
    }
    // this.getPageMenu()
    this.getArea()
    this.getSzcjToken()
  },
  methods: {
    //获取用户所属区县
    getArea() {
      getUserArea().then((res) => {
        if (res.code == 200) {
          localStorage.setItem('city', res.data.area)
          localStorage.setItem('adminCity', res.data.area)
        }
      })
    },
    async initPocVideoData() {
      try {
        const authInfo = await mainObj.queryAuth()
        console.log('POC 认证信息:', authInfo)

        // 继续处理视频数据...
      } catch (error) {
        console.error('初始化 POC 视频数据失败:', error)
      }
    },
    getPageMenu() {
      indexApi('/xzzfj_sy_cd', { area: localStorage.getItem('adminCity') }).then((res) => {
        this.pageArr = res.data
      })
    },
    getSzcjToken() {
      getSzcjToken({
        username: '13735713555',
        password: Encrypt('%&fyAB2%mZ'),
      }).then((res) => {
        console.log('szcjToken', res.token)
        this.szcjToken = res.token
      })
    },
    pageJump(item) {
      switch (item.name) {
        case '绩效评估':
          getUrl('/token/getTokenInfo', { jmppage: 'ks' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '三色预警一张图':
          getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '行政执法综合评价一张图':
          getUrl({ type: 'dashboard', module: 'xzzfpjzb' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '首页':
          this.getPage(item)
          break
        case '城市运行管理服务首页':
          this.getPage({ key: 'csyxgl' })
          break
        case '驾驶舱':
          this.getPage(item)
          break
        case '指挥调度一张图':
          this.getPage(item)
          break
        case '执法态势一张图':
          this.getPage(item)
          break
        case '行政执法一张图':
          this.getPage(item)
          break
        case '城市管理一张图':
          this.getPage(item)
          break
        case '物联网设备一张图':
          this.getPage(item)
          break
        case '公众服务一张图':
          this.getPage(item)
          break
        case '综合评价一张图':
          this.getPage(item)
          break
        case '运行监测一张图':
          this.getPage(item)
          break
        case '犬类管理一张图':
          this.getPage(item)
          break
        case '站前执法一张图':
          this.getPage(item)
          break
        case '县级中心':
          this.getPage(item)
          break
        case '应用集成':
          this.getPage(item)
          break
        case '后台管理':
          this.openHtmlByMode(
            process.env.NODE_ENV === 'production'
              ? 'http://10.45.13.116/ygf/login'
              : 'http://10.45.13.116:8000/ygf-web/login'
          )
          break
        case '案件回访':
          this.getPage(item)
          break
        case '数字城建一张图':
          this.openHtmlByMode('https://jhqy.jsj.jinhua.gov.cn/dplus/view/1672878871176626178?token=' + this.szcjToken)
        case '垃圾分类一张图':
          loginByThirdComplete({
            tenantCode: 'TENANT_JHCC',
            account: 'dddl',
            scope: 'THIRD_APP',
            appKey: '25jhljfl05ygf29',
            appSecret: 'B7FEAAEAD3AD3ED37468A45EF8030E94',
          }).then((res) => {
            if (res.msg == '登录成功') {
              if (res.data.access_token) {
                let access_token = res.data.access_token
                this.openHtmlByMode(`http://ljfl.xzzfj.jinhua.gov.cn/#/autoLogin?token=${access_token}`)
              }
            }
          })
          break
      }
    },
    getPage(item) {
      this.$router.push('/' + item.key)
    },
    openHtmlByMode(url) {
      window.open(url)
    },
    //获取端口号
    getCurrentPortWithDefault() {
      let port = window.location.port
      if (port === '') {
        if (window.location.protocol === 'http:') {
          port = '80'
        } else if (window.location.protocol === 'https:') {
          port = '443'
        }
      }
      return port
    },
  },
  watch: {},
}
</script>

<style scoped>
.choosePage {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/pageMenu/page_bkg.png');
  background-size: cover;
  background-position: center;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}
.name {
  width: 100%;
  text-align: center;
  font-size: 64px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #e3f3ff;
  margin-top: 70px;
  margin-left: 20px;
}

.tops {
  width: 100%;
  height: fit-content;
  padding: 0 171px;
  box-sizing: border-box;
  display: flex;
  justify-content: right;
  position: absolute;
  top: 75px;
}
.topItem {
  width: 420px;
  height: 172px;
  margin-left: 80px;
  cursor: pointer;
  background-size: 100% 100% !important;
}

/* 顶部区域 */
.header {
  width: 100%;
  height: 120px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 80px;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
}

.title {
  font-size: 48px;
  font-family: YouSheBiaoTiHei, sans-serif;
  font-weight: 400;
  color: #00d4ff;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
  background: linear-gradient(90deg, #00d4ff 0%, #0099cc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.top-right {
  display: flex;
  align-items: center;
}

.admin-btn {
  padding: 12px 30px;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 153, 204, 0.2) 100%);
  border: 2px solid #00d4ff;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.admin-btn:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.4) 0%, rgba(0, 153, 204, 0.4) 100%);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
  transform: translateY(-2px);
}

.admin-btn span {
  font-size: 24px;
  font-family: YouSheBiaoTiHei, sans-serif;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 80px;
  position: relative;
}

/* 左侧区域 */
.left-section {
  width: 400px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 5;
}

.section-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.section-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.8));
}

.section-title {
  font-size: 36px;
  font-family: YouSheBiaoTiHei, sans-serif;
  font-weight: 400;
  color: #00d4ff;
  text-align: center;
  margin-bottom: 50px;
  text-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
  line-height: 1.2;
}

.left-menu,
.right-menu {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.menu-item {
  padding: 15px 25px;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 153, 204, 0.1) 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  font-size: 20px;
  font-family: YouSheBiaoTiHei, sans-serif;
  color: #e3f3ff;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.menu-item:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 153, 204, 0.3) 100%);
  border-color: #00d4ff;
  color: #ffffff;
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, #00d4ff 0%, #0099cc 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.menu-item:hover::before {
  transform: scaleY(1);
}
/* 中央区域 */
.center-section {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin: 0 50px;
}

/* 右侧区域 */
.right-section {
  width: 400px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 5;
}

.right-menu .menu-item:hover {
  transform: translateX(-5px);
}

.right-menu .menu-item::before {
  right: 0;
  left: auto;
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .title {
    font-size: 40px;
  }

  .left-section,
  .right-section {
    width: 350px;
  }

  .section-title {
    font-size: 32px;
  }

  .menu-item {
    font-size: 18px;
    padding: 12px 20px;
  }
}

@media (max-width: 1600px) {
  .main-content {
    padding: 0 40px;
  }

  .left-section,
  .right-section {
    width: 300px;
  }

  .section-title {
    font-size: 28px;
  }

  .menu-item {
    font-size: 16px;
    padding: 10px 15px;
  }
}

/* 字体定义 */
@font-face {
  font-family: YouSheBiaoTiHei;
  src: url('@/assets/fonts/YouSheBiaoTiHei-2.ttf');
  font-weight: normal;
  font-style: normal;
}

/* 全局动画效果 */
.choosePage * {
  box-sizing: border-box;
}

/* 添加一些科技感的装饰元素 */
.choosePage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}
</style>
