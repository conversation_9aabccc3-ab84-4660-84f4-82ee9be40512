<template>
  <div>
    <CommonTitle text='行政检查'></CommonTitle>
    <div class="zhcyc">
      <div
        class="quan"
        :class="`quan${index==0?2:index==2?0:index}`"
        v-for="(item,index) in xzjcData"
        :key="index"
      >
        <li>{{item.name}}</li>
        <li :class="`txt${index}`">{{item.value}}{{item.unit}}</li>
      </div>
    </div>
    <CommonTitle2 text='检查计划统筹'></CommonTitle2>
    <div class="ptzy_box box">
      <div class="ptzy_item" v-for="(item,index) in jcjhtcList">
        <img :src="item.icon" alt="" width="89" height="100" />
        <div style="margin-top: 20px;margin-left: 10px;">
          <div class="item_name">{{item.name}}</div>
          <div class="item_bottom">
              <span
                class="s-font-45 xt_font"
                :class="index%3==1?'s-blue':'s-yellow'"
              >{{item.value}}</span>
            <span
              style='margin-left: 10px;'
              class="s-font-25"
              :class="index%3==1?'s-blue':'s-yellow'"
            >{{item.unit}}</span>
          </div>
        </div>
      </div>
    </div>
    <CommonTitle2 text='检查任务统筹'></CommonTitle2>
    <div class="ptzy_box box">
      <div class="ptzy_item" v-for="(item,index) in jcrwtcList">
        <img :src="item.icon" alt="" width="89" height="100" />
        <div style="margin-top: 20px;margin-left: 10px;">
          <div class="item_name">{{item.name}}</div>
          <div class="item_bottom">
              <span
                class="s-font-45 xt_font"
                :class="index%3==1?'s-blue':'s-yellow'"
              >{{item.value}}</span>
            <span
              style='margin-left: 10px;'
              class="s-font-25"
              :class="index%3==1?'s-blue':'s-yellow'"
            >{{item.unit}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import CommonTitle2 from '@/components/CommonTitle2'
import {indexApi} from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle,
    CommonTitle2
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      //行政检查
      xzjcData: [],
      name:"",
      dialogShow: false,

      jcjhtcList: [
        {
          icon: require("@/assets/zfts/年度计划数icon.png"),
          name: "年度计划数",
          value: 276,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/跨部门计划数icon.png"),
          name: "跨部门计划数",
          value: 160,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/计划完成数icon.png"),
          name: "计划完成数",
          value: 66,
          unit: "个",
        }
      ],
      jcrwtcList: [
        {
          icon: require("@/assets/zfts/总任务数icon.png"),
          name: "总任务数",
          value: 1075,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/综合查一次icon.png"),
          name: "“综合查一次”任务数",
          value: 168,
          unit: "个",
        },
        {
          icon: require("@/assets/zfts/任务完成数icon.png"),
          name: "任务完成数",
          value: 935,
          unit: "个",
        }
      ],
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      //行政检查
      // indexApi("/csdn_yjyp1", { area_code: city,sjwd2: year }).then((res) => {
      //   this.xzjcData = res.data.map((a,i) => ({
      //     name: a.label.split("-")[1],
      //     value: a.num,
      //     unit: a.unit,
      //   }))
      // });

      this.xzjcData = [
        {
          name: "“综合查一次”实施率",
          value: 15.62,
          unit: "%",
        },
        {
          name: "现场检查户次",
          value: 20397,
          unit: "次",
        },
        {
          name: "监管事项数",
          value: 3465,
          unit: "个",
        },
        {
          name: "应用信用规则率",
          value: 100,
          unit: "%",
        },
        {
          name: "亮码检查率",
          value: 99.82,
          unit: "%",
        }
      ]
    },
    showjcdxDialog(item) {
      this.name = item.name;
      this.dialogShow = true
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
/* 行政检查 */
.zhcyc {
  width: 100%;
  height: 460px;
  padding: 20px 60px;
  box-sizing: border-box;
  position: relative;
  background: url("@/assets/zfts/zhcyc-bg.png") no-repeat;
  background-size: 100% 100%;
}
.quan {
  cursor: pointer;
}

.quan0 {
  position: absolute;
  left: 339px;
  top: -10px;
  width: 308px;
  height: 260px;
  background: url("@/assets/zfts/zrw.png") no-repeat;
  background-size: 100% 100%;
  font-size: 36px;
  color: #fff;
  text-align: center;
  line-height: 60px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan1 {white-space: nowrap;
  position: absolute;
  left: 100px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("@/assets/zfts/rwzb.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan2 {white-space: nowrap;
  position: absolute;
  right: 170px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("@/assets/zfts/cybm.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan3 {
  position: absolute;
  left: 150px;
  top: 200px;
  width: 186px;
  height: 186px;
  background: url("@/assets/zfts/jchc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan4 {
  position: absolute;
  right: 210px;
  top: 195px;
  width: 179px;
  height: 179px;
  background: url("@/assets/zfts/jsqygr.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;padding: 50px 0px;
  box-sizing: border-box;
}

.quan5 {
  position: absolute;
  left: 400px;
  top: 254px;
  width: 197px;
  height: 197px;
  background: url("@/assets/zfts/rwaswc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding: 40px;
  box-sizing: border-box;
}

.txt0 {
  color: #d958de;
  font-size: 30px;
  font-weight: bold;
}

.txt1 {
  color: #08a0f5;
  font-size: 30px;
  font-weight: bold;
}

.txt2 {
  color: #eed252;
  font-size: 40px;
  font-weight: bold;
}

.txt3 {
  color: #00fffc;
  font-size: 30px;
  font-weight: bold;
}

.txt4 {
  color: #45f95e;
  font-size: 30px;
  font-weight: bold;
}

.txt5 {
  color: #ffb436;
  font-size: 30px;
  font-weight: bold;
}

.quan0 {
  animation: move infinite 5s;
}
.quan1 {
  animation: move infinite 5s 0.5s;
}
.quan2 {
  animation: move infinite 5s 1.2s;
}
.quan3 {
  animation: move infinite 5s 1s;
}
.quan4 {
  animation: move infinite 5s 0.5s;
}
@keyframes move {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.ptzy_box {
  width: 100%;
  height: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20px 70px;
  margin: 40px 0 40px 0;
}
.ptzy_item {
  display: flex;
  width: 30%;
  margin-bottom: 10px;
}
.box {
  padding: 20px 40px;
  box-sizing: border-box;
  position: relative;
}
.item_name {
  width: 200px;
  height: auto;
  font-size: 28px;
  color: #d1d6df;
  margin-top: -10px;
}
.xt_font {
  font-family: DINCondensed;
  font-style: italic;
}
.s-yellow {
  color: #eed252;
}
.s-blue {
  color: #34dfe3;
}
</style>