<template>
  <div>
    <CommonTitle text="执法协同"></CommonTitle>
    <div class="zfxt-con">
      <div
        style="position: absolute; top: -75px; left: 680px; z-index: 2"
        v-show="year == $currentYear"
        class="yearChange"
      >
        <el-date-picker
          v-model="value1"
          type="monthrange"
          @change="changeValue1"
          range-separator="-"
          start-placeholder="开始月份"
          value-format="yyyy-MM"
          end-placeholder="结束月份"
          :append-to-body="false"
        ></el-date-picker>
      </div>
      <div v-show="!showZFXT" id="zfxt" class="s-font-30 s-c-white s-text-center" style="line-height: 300px">
        暂无数据
      </div>
      <div class="zfxtContainer" v-show="showZFXT">
        <div class="zfxtIndexs">
          <div class="zfxt-item" v-for="(item, i) in zfxtData">
            <div class="zfxtNumberText1" :class="{ zfxtNumberText2: i % 2 == 0 }">{{ item.count }}</div>
            <div class="zfxt-text">{{ item.ywwd1 }}</div>
            <div class="yellowLine" :class="{ blueLine: i % 2 == 0 }"></div>
          </div>
        </div>
        <div class="base"></div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { indexApi } from '@/api/indexApi'
import moment from 'moment'
export default {
  name: 'index',
  components: {
    CommonTitle,
  },
  data() {
    return {
      value1: [new Date().getFullYear() + '-01', moment(new Date()).format('YYYY-MM')],
      year: localStorage.getItem('year'),
      city: localStorage.getItem('city'),
      showZFXT: false,
      //执法协同
      zfxtData: [
        {
          name: '抄告抄送',
          value: 189,
          zb: 50,
          itemStyle: {},
        },
        {
          name: '联合会商',
          value: 184,
          zb: 9.97,
          itemStyle: {},
        },
        {
          name: '基层治理协同',
          value: 144,
          zb: 40.03,
          itemStyle: {},
        },
        {
          name: '联合执法',
          value: 532,
          zb: 40.03,
          itemStyle: {},
        },
        {
          name: '紧急任务',
          value: 1,
          zb: 40.03,
          itemStyle: {},
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city
      this.initApi(city, localStorage.getItem('year'))
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year
      this.initApi(localStorage.getItem('city'), year)
    })
    this.initApi(localStorage.getItem('city'), localStorage.getItem('year'))
  },
  methods: {
    changeValue1(v) {
      indexApi('/yjyp_zfts_zfxt', {
        qxwd: this.city == '金华市' ? '' : this.city,
        sjwd1: v[0],
        sjwd2: v[1],
      }).then((res) => {
        if (res.data.length > 0) {
          this.showZFXT = true
          this.zfxtData = res.data
          // this.getChart02('zfxt', res)
        } else {
          this.showZFXT = false
        }
      })
    },
    initApi(city, year) {
      this.value1 = this.$getYearList(year)
      indexApi('/yjyp_zfts_zfxt', {
        qxwd: city == '金华市' ? '' : city,
        sjwd1: this.value1[0],
        sjwd2: this.value1[1],
      }).then((res) => {
        if (res.data.length > 0) {
          this.showZFXT = true
          this.zfxtData = res.data
          // this.getChart02('zfxt', res)
        } else {
          this.showZFXT = false
        }
      })
    },
    //执法协同
    getChart02(id, echartsData) {
      echartsData = echartsData.data.map((item) => {
        return {
          name: item.ywwd1,
          count: item.count,
        }
      })
      let myChart = this.$echarts.init(document.getElementById(id))
      let selectedIndex = ''
      let hoveredIndex = ''
      let option = getPie3D(echartsData, 0.59)
      // 生成扇形的曲面参数方程
      function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, h) {
        // 计算
        const midRatio = (startRatio + endRatio) / 2

        const startRadian = startRatio * Math.PI * 2
        const endRadian = endRatio * Math.PI * 2
        const midRadian = midRatio * Math.PI * 2

        // 如果只有一个扇形，则不实现选中效果。
        if (startRatio === 0 && endRatio === 1) {
          // eslint-disable-next-line no-param-reassign
          isSelected = false
        }

        // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
        // eslint-disable-next-line no-param-reassign
        k = typeof k !== 'undefined' ? k : 1 / 3

        // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
        const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
        const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

        // 计算高亮效果的放大比例（未高亮，则比例为 1）
        const hoverRate = isHovered ? 1.05 : 1

        // 返回曲面参数方程
        return {
          u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32,
          },

          v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
          },

          x(u, v) {
            if (u < startRadian) {
              return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
              return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
          },

          y(u, v) {
            if (u < startRadian) {
              return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            if (u > endRadian) {
              return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
          },

          z(u, v) {
            if (u < -Math.PI * 0.5) {
              return Math.sin(u)
            }
            if (u > Math.PI * 2.5) {
              return Math.sin(u) * h * 0.1
            }
            // 当前图形的高度是Z根据h（每个value的值决定的）
            return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
          },
        }
      }
      // 生成模拟 3D 饼图的配置项
      function getPie3D(pieData, internalDiameterRatio) {
        const series = []
        // 总和
        let sumValue = 0
        let startValue = 0
        let endValue = 0
        const legendData = []
        let legend = []
        const k =
          typeof internalDiameterRatio !== 'undefined'
            ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
            : 1 / 3

        // 为每一个饼图数据，生成一个 series-surface 配置
        for (let i = 0; i < pieData.length; i += 1) {
          sumValue += pieData[i].count

          const seriesItem = {
            name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
            type: 'surface',
            parametric: true,
            wireframe: {
              show: false,
            },
            pieData: pieData[i],
            pieStatus: {
              selected: false,
              hovered: false,
              k,
            },
          }

          if (typeof pieData[i].itemStyle !== 'undefined') {
            const { itemStyle } = pieData[i]

            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.color !== 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
            // eslint-disable-next-line no-unused-expressions
            typeof pieData[i].itemStyle.opacity !== 'undefined'
              ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
              : null

            seriesItem.itemStyle = itemStyle
          }
          series.push(seriesItem)
        }
        // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
        // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
        for (let i = 0; i < series.length; i += 1) {
          endValue = startValue + series[i].pieData.count

          series[i].pieData.startRatio = startValue / sumValue
          series[i].pieData.endRatio = endValue / sumValue
          series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            // 我这里做了一个处理，使除了第一个之外的值都是10
            series[i].pieData.count === series[0].pieData.count ? 35 : 10
          )

          startValue = endValue

          legendData.push(series[i].name)
        }

        // 准备待返回的配置项，把准备好的 legendData、series 传入。
        const option = {
          // animation: false,
          tooltip: {
            formatter: (params) => {
              if (params.seriesName !== 'mouseoutSeries') {
                return `${
                  params.seriesName
                }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  params.color
                };"></span>${option.series[params.seriesIndex].pieData.count} `
              }
              return ''
            },
            textStyle: {
              color: '#ffff',
              fontSize: 24,
            },
            borderWidth: 0,
            backgroundColor: 'rgba(51, 51, 51, 0.7)',
          },
          legend: {
            right: '10%',
            top: '11%',
            orient: 'vertical',
            textStyle: {
              rich: {
                name: {
                  fontSize: 30,
                  color: '#ffffff',
                  padding: [0, 0, 0, 15],
                },
                value: {
                  fontSize: 30,
                  color: '#e9d0ab',
                  padding: [10, 5, 0, 15],
                },
                value1: {
                  fontSize: 30,
                  color: '#e9d0ab',
                  padding: [10, 5, 0, 15],
                },
              },
            },
            formatter: function (name) {
              var data = option.series //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].pieData.count
                if (data[i].pieData.name == name) {
                  tarValue = data[i].pieData.count
                }
              }
              var p = ((tarValue / total) * 100).toFixed(2)
              // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
              return '{name|' + name + '}{value1|' + tarValue + '}{value|' + p + '%}'
            },
            // padding: [0, 600, 0, 200],
          },
          xAxis3D: {
            min: -1,
            max: 1,
          },
          yAxis3D: {
            min: -1,
            max: 1,
          },
          zAxis3D: {
            min: -1,
            max: 1,
          },
          grid3D: {
            show: false,
            z: 1,
            boxHeight: 10,
            top: '-16%',
            left: '-24%',
            viewControl: {
              // 3d效果可以放大、旋转等，请自己去查看官方配置
              alpha: 25,
              // beta: 30,
              rotateSensitivity: 1,
              zoomSensitivity: 0,
              panSensitivity: 0,
              autoRotate: true,
              distance: 150,
            },
            // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
            postEffect: {
              // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
              enable: false,
              bloom: {
                enable: true,
                bloomIntensity: 0.1,
              },
              SSAO: {
                enable: true,
                quality: 'medium',
                radius: 2,
              },
              // temporalSuperSampling: {
              //   enable: true,
              // },
            },
          },
          series,
        }
        return option
      }
      //  修正取消高亮失败的 bug
      // 监听 mouseover，近似实现高亮（放大）效果

      // 修正取消高亮失败的 bug

      myChart.setOption(option)
    },
  },
  watch: {},
}
</script>

<style scoped lang='less'>
/* 执法协同 */
.zfxt-con {
  width: 100%;
  height: 500px;
  position: relative;
  /deep/ .yearChange {
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
    }
    .el-picker-panel {
      top: 160px !important;
      left: -320px !important;
    }
  }
}

#zfxt {
  width: 1030px;
  height: 500px;
  position: relative;
}

.zfxt-bg {
  position: absolute;
  left: 70px;
  top: 95px;
  width: 389px;
  height: 163px;
  background: url('@/assets/zfts/chart-bg.png') no-repeat;
  background-size: 100% 100%;
}

.base {
  width: 877px;
  height: 125px;
  background: url("@/assets/zfts/底盘.png");
  background-size: cover;
  margin-top: 277px;
}

.zfxtContainer {
  width: 100%;
  height: 480px;
  overflow: hidden;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.zfxtIndexs {
  width: 100%;
  height: 340px;
  position: absolute;
  top: 0;
  display: flex;
  justify-content: space-evenly;
  align-items: flex-end;
  flex-wrap: wrap;
}
.zfxt-item {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-direction: column;
  width: 100px;
  height: fit-content;
  flex-shrink: 0;
}
.zfxtNumberText1 {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #eed252;
  text-align: center;
}
.zfxt-text {
  width: 100px;
  font-size: 24px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #acb8c5;
  white-space: pre-wrap;
  text-align: center;
}
.blueLine {
  width: 18px !important;
  height: 75px !important;
  background-size: cover !important;
  background: url('@/assets/zfts/blueLine.png') no-repeat !important;
}

.yellowLine {
  width: 18px;
  height: 174px;
  background-size: cover;
  background: url('@/assets/zfts/yellowLine.png') no-repeat;
}
.zfxtNumberText2 {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #34dfe3;
  text-align: center;
}
</style>