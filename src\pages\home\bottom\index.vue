<template>
  <div class='bottom'>
    <div class="bottom-container" v-cloak>
      <div class="left-container">
        <CommonTitle2 text='处罚事项TOP10'></CommonTitle2>
        <div class="table" style="height: 420px">
          <div class="th">
            <div class="th_td" style="flex: 0.2">序号</div>
            <div class="th_td" style="flex: 0.4">事项名称</div>
            <div class="th_td" style="flex: 0.4">案件数</div>
          </div>
          <div
            class="tbody"
            id="box1"
            @mouseover="mouseenterEvent1()"
            @mouseleave="mouseleaveEvent1()"
          >
            <div class="tr" v-for="(item,index) in tableData1" :key="index">
              <div class="tr_td" style="flex: 0.2">{{index+1}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.label}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.num}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-container">
        <CommonTitle2 text='处罚案件'></CommonTitle2>
        <div class="table" style="height: 420px">
          <div class="th">
            <div class="th_td" style="flex: 0.2">序号</div>
            <div class="th_td" style="flex: 0.4">领域</div>
            <div class="th_td" style="flex: 0.4">案件数</div>
          </div>
          <div
            class="tbody"
            id="box2"
            @mouseover="mouseenterEvent2()"
            @mouseleave="mouseleaveEvent2()"
          >
            <div class="tr" v-for="(item,index) in tableData2" :key="index">
              <div class="tr_td" style="flex: 0.2">{{index+1}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.ywwd1}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.num}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommonTitle2 from '@/components/CommonTitle2'
import { indexApi } from '@/api/indexApi'
export default {
  name: 'index',
  components: {
    CommonTitle2
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      time1: null,
      dom1: null,
      time2: null,
      dom2: null,
      tableData1: [],
      tableData2: [],
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
    // 表格滚动
    this.dom1 = document.getElementById("box1");
    this.mouseleaveEvent1();
    this.dom2 = document.getElementById("box2");
    this.mouseleaveEvent2();
  },
  methods: {
    initApi(city,year) {
      indexApi("/csdn_yjyp10", { area_code: city,sjwd2: year }).then((res) => {
        this.tableData2 = res.data.sort(function (a, b) {
          return b.num - a.num;
        });
      });
      indexApi("/csdn_yjyp17", { area_code: city,sjwd2: year }).then((res) => {
        this.tableData1 = res.data.sort(function (a, b) {
          return b.num - a.num;
        });
      });
    },

    mouseenterEvent1() {
      clearInterval(this.time1);
    },
    mouseleaveEvent1() {
      this.time1 = setInterval(() => {
        this.dom1.scrollBy({
          top: 86,
          behavior: "smooth",
        });
        if (
          this.dom1.scrollTop >=
          this.dom1.scrollHeight - this.dom1.offsetHeight
        ) {
          this.dom1.scrollTop = 0;
        }
      }, 1500);
    },
    mouseenterEvent2() {
      clearInterval(this.time2);
    },
    mouseleaveEvent2() {
      this.time2 = setInterval(() => {
        this.dom2.scrollBy({
          top: 86,
          behavior: "smooth",
        });
        if (
          this.dom2.scrollTop >=
          this.dom2.scrollHeight - this.dom2.offsetHeight
        ) {
          this.dom2.scrollTop = 0;
        }
      }, 1500);
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .bottom-container {
    width: 1760px;
    height: 525px;
    padding: 20px;
    box-sizing: border-box;
    background: url("@/assets/index/bottom-bg.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    .left-container {
      width: 60%;
    }
    .right-container {
      width: 40%;
    }
    /* 表格 */
    .table {
      width: 100%;
      /* height: 500px; */
      padding: 10px 30px;
      box-sizing: border-box;
    }

    .table .th {
      width: 100%;
      height: 85px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      font-weight: 700;
      font-size: 32px;
      line-height: 60px;
      color: #CDE7FF;
      background: #082B62;
    }

    .table .th_td {
      letter-spacing: 0px;
      text-align: center;
    }

    .table .tbody {
      width: 100%;
      height: calc(100% - 59px);
      /* overflow-y: auto; */
      overflow: hidden;
    }

    .table .tbody:hover {
      overflow-y: auto;
    }

    .table .tbody::-webkit-scrollbar {
      width: 4px;
      /*滚动条整体样式*/
      height: 4px;
      /*高宽分别对应横竖滚动条的尺寸*/
    }

    .table .tbody::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #20aeff;
      height: 8px;
    }

    .table .tr {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      height: 85px;
      line-height: 70px;
      font-size: 32px;
      color: #CDE7FF;
      cursor: pointer;
      border-top: 1px solid #959aa1;
      border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
      1;
      box-sizing: border-box;
    }

    .table .tr:nth-child(2n) {
      background: #072249;
    }

    .table .tr:nth-child(2n + 1) {
      background: #081B3C;
    }

    .table .tr:hover {
      background-color: #0074da75;
    }

    .table .tr_td {
      letter-spacing: 0px;
      text-align: center;
      box-sizing: border-box;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
</style>