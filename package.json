{"name": "jh-platform-screen", "version": "2.0.0", "private": true, "scripts": {"serve-dev": "vue-cli-service serve --mode development", "serve-prd": "vue-cli-service serve --mode production", "build-dev": "vue-cli-service build --mode development", "build-test": "vue-cli-service build --mode staging", "build-prd": "vue-cli-service build --no-module --mode production", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@arcgis/core": "4.26", "@easydarwin/easywasmplayer": "^4.0.13", "@turf/turf": "^6.5.0", "animate.css": "^4.1.1", "animejs": "^3.2.1", "axios": "^0.19.2", "compression-webpack-plugin": "^6.1.1", "core-js": "^3.8.3", "dingtalk-jsapi": "^3.0.9", "echarts": "^5.3.2", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.8", "image-conversion": "^2.1.1", "js-cookie": "2.2.1", "jsencrypt": "^3.3.2", "jspdf": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.30.1", "quill": "1.3.7", "svg-sprite-loader": "^6.0.11", "vue": "^2.6.14", "vue-count-to": "^1.0.13", "vue-dynamic-marquee": "^0.1.7", "vue-pubilc-layer": "^2.0.5", "vue-router": "^3.3.4", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.6.2", "yarn": "^1.22.19"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-component": "^1.1.1", "babel-polyfill": "^6.26.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.1.2", "less-loader": "^6.1.1", "postcss": "^8.5.3", "query-string": "^7.1.1", "sass": "^1.83.0", "sass-loader": "^16.0.4", "vue-loader": "^15.9.8", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"no-debugger": "off", "no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11", "chrome >= 63", "safari >= 12", "ie >= 8"]}