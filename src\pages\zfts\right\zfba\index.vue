<template>
  <div>
    <CommonTitle text='执法办案'></CommonTitle>
    <div class="zfba">
      <li v-for="(item,index) in zfbaData" :key="index" @click="showAnjianDialog(item)">
        <img :src="item.icon" alt="" />
        <div class="text-con">
          <div class="text-bg"><span class="text">{{item.name}}</span></div>
          <div style="line-height: 50px">
            {{item.value}}<span class="unit">{{item.unit}}</span>
          </div>
        </div>
      </li>
    </div>
    <!-- 案件回访 -->
    <div class="ajhf">
      <div class="left-con" @click="changePage" style="cursor: pointer">
        <img src="@/assets/zfts/ajhf.png" alt="" />
        <div class="aj-t">案件回访</div>
      </div>
      <div class="right-con" style="position: relative">
        <img src="@/assets/zfts/myd-bg.png" alt="" />
        <div class="aj-t myd">
          <div>满意度</div>
          <div style="color: #eed252" class="xt-font">{{my}}</div>
        </div>
        <div class="aj-t hfsl">
          <div>回访数量</div>
          <div style="color: cyan" class="xt-font">{{hfsl}}</div>
        </div>
      </div>
    </div>

    <zfbaDialog :name='name' :visible='dialogShow' @close='dialogShow = false'></zfbaDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import zfbaDialog from './zfbaDialog'
import {indexApi} from '@/api/indexApi'
import { getHfgkList } from '@/api/zfts'
export default {
  name: 'index',
  components: {
    CommonTitle,
    zfbaDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      //执法办案
      zfbaData: [],
      hfsl:"",
      my:"",

      name:"",
      dialogShow: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(city,year) {
      // 执法办案
      indexApi("/csdn_yjyp2", { area_code: city,sjwd2: year }).then((res) => {
        this.zfbaData = res.data.map((a,i) => ({
          icon: require(`@/assets/zfts/${
            i == 0 ? "ajzs" : i == 1 ? "jy" : i == 2 ? "zdaj" : i == 3 ? "cfje" : i == 4 ? "sxfg" : "jy"
          }.png`),
          name: a.label.includes("-") ? a.label.split("-")[1] : a.label,
          value: a.num,
          unit: a.unit,
        }))
      });

      getHfgkList({
        xsq: city,
        startTime: this.$getYearList(year)[0],
        endTime: this.$getYearList(year)[1],
      }).then(res => {
        if (res.code == 200) {
          this.my = res.data.myd + "%"
          this.hfsl = res.data.fhzs
        }
      })
    },
    showAnjianDialog(item) {
      this.name = item.name
      this.dialogShow = true
    },
    changePage() {
      this.$router.push("/ajhf")
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.zfba {
  width: 100%;
  height: 270px;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 0px 50px;
  box-sizing: border-box;
}
.text-con {
  font-size: 32px;
  line-height: 48px;
  margin-top: 10px;
  color: #eed252;
  font-weight: bold;
  font-style: italic;
}

.text-bg {
  width: 250px;
  height: 48px;
}

.text {
  font-style: normal;
  color: #d1d6df;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unit {
  font-size: 20px;
  margin-left: 10px;
}
/* 案件回访 */
.ajhf {
  width: 100%;
  height: 200px;
  padding: 0px 50px;
  box-sizing: border-box;
  display: flex;
}

.aj-t {
  font-size: 36px;
  color: #fff;
  text-align: center;
  margin-top: -35px;
}

.myd {
  position: absolute;
  top: 15px;
  left: 175px;
  line-height: 100px;
}

.hfsl {
  position: absolute;
  top: 15px;
  right: 175px;
  line-height: 100px;
}

#ajhf-chart {
  width: 100%;
  height: 350px;
  margin: 20px 0px;
}

.xt-font {
  font-family: DINCondensed;
  font-size: 56px;
  font-style: italic;
}

.zfba li {
  width: 33%;
  height: 120px;
  display: flex;
}
.zfba img {
  width: 92px;
  height: 101px;
}
</style>