import { requestThirdParty } from '@/utils/request'

/**
 * 第三方登录接口
 * @param {Object} params - 登录参数
 * @param {string} params.tenantCode - 租户代码
 * @param {string} params.account - 账户
 * @param {string} params.scope - 作用域
 * @param {Object} signHeaders - 签名头部信息
 * @param {string} signHeaders.vtxSignAppKey - 应用密钥
 * @param {string} signHeaders.vtxSignTimestamp - 时间戳
 * @param {string} signHeaders.vtxSign - 签名
 * @returns {Promise} 返回登录结果
 */
export function loginByThird(params, signHeaders) {
  const { tenantCode, account, scope } = params;
  const { vtxSignAppKey, vtxSignTimestamp, vtxSign } = signHeaders;

  return requestThirdParty({
    url: 'http://ljfl.xzzfj.jinhua.gov.cn/cloud/management/api/v101/login/loginByThird',
    method: 'POST',
    headers: {
      'vtxSignAppKey': vtxSignAppKey,
      'vtxSignTimestamp': vtxSignTimestamp,
      'vtxSign': vtxSign
    },
    data: {
      tenantCode,
      account,
      scope
    }
  });
}

/**
 * 生成签名时间戳
 * @returns {string} 当前时间戳
 */
export function generateTimestamp() {
  return Date.now().toString();
}

/**
 * 第三方登录的便捷方法 - 自动生成时间戳
 * @param {Object} params - 登录参数
 * @param {string} params.tenantCode - 租户代码
 * @param {string} params.account - 账户
 * @param {string} params.scope - 作用域
 * @param {Object} signConfig - 签名配置
 * @param {string} signConfig.appKey - 应用密钥
 * @param {string} signConfig.sign - 签名（需要根据业务逻辑生成）
 * @returns {Promise} 返回登录结果
 */
export function loginByThirdAuto(params, signConfig) {
  const timestamp = generateTimestamp();
  
  return loginByThird(params, {
    vtxSignAppKey: signConfig.appKey,
    vtxSignTimestamp: timestamp,
    vtxSign: signConfig.sign
  });
}