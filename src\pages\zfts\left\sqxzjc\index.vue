<template>
  <div>
    <CommonTitle text='涉企行政检查'></CommonTitle>
    <div class="jcjh">
      <div class="jhzs" v-for='(item,i) in sqxzjcData' :key='i' :style="{backgroundImage: 'url(' + item.icon + ')',marginTop: '30px'}">
        <li class="text">{{item.label}}</li>
        <li class="value">
          <count-to
            :start-val="0"
            :end-val="Number(item.number)"
            :duration="3000"
            class="count-toNum s-c-yellow-gradient"
          >
          </count-to>
          <span class="unit">{{item.unit}}</span>
        </li>
      </div>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
import CommonTitle from '@/components/CommonTitle'
export default {
  name: 'index',
  components: {
    CommonTitle,
    countTo
  },
  data() {
    return {
      sqxzjcData: [
        {
          label:"涉企检查户次",
          number:"3965",
          unit: "次",
          icon: require("@/assets/zfts/市区检查户次.png")
        },
        {
          label:"减少企业干扰户",
          number:"640",
          unit: "次",
          icon: require("@/assets/zfts/减少企业干扰户.png")
        },
        {
          label:"涉企“综合查一次”户次",
          number:"2526",
          unit: "次",
          icon: require("@/assets/zfts/涉企“综合查一次”户次.png")
        },
        {
          label:"涉企“综合查一次”实施率",
          number:"63.7",
          unit: "%",
          icon: require("@/assets/zfts/涉企“综合查一次”实施率.png")
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang='less'>
.jcjh {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
  box-sizing: border-box;
  padding: 50px 60px;
}
.text {
  width: 252px;
  height: auto;
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-style: italic;
  color: #d1d6df;
  line-height: 50px;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.value {
  width: 260px;
  height: 44px;
  font-size: 60px;
  font-family: DINCondensed;
  font-weight: bold;
  font-style: italic;
  color: #eed252;
}

.unit {
  font-size: 35px;
  margin-left: 10px;
}
.jhzs {
  width: 431px;
  min-height: 169px;
  background-size: 100% 100%;
  padding-left: 150px;
  padding-top: 15px;
  padding-bottom: 45px;
  box-sizing: border-box;
}
.zdbm {
  width: 431px;
  height: 150px;
  background-image: url('@/assets/zfts/zdbm.png');
  background-size: 100% 100%;
}

</style>