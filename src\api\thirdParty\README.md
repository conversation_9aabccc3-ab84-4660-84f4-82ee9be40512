# 第三方登录接口封装

本模块封装了第三方登录接口，支持向 `http://ljfl.xzzfj.jinhua.gov.cn/cloud/management/api/v101/login/loginByThird` 发送登录请求。

## 功能特点

- ✅ 支持 `application/x-www-form-urlencoded` 格式的数据提交
- ✅ 自动处理签名头部（vtxSignAppKey、vtxSignTimestamp、vtxSign）
- ✅ 独立的axios实例，不受全局拦截器影响
- ✅ 完善的错误处理机制
- ✅ 提供便捷方法和完整示例

## 安装和导入

```javascript
// 导入主要方法
import { loginByThird, loginByThirdAuto, generateTimestamp } from '@/api/thirdParty/login'

// 或者从统一的API入口导入
import { loginByThird, loginByThirdAuto } from '@/api/indexApi'
```

## API 方法

### 1. loginByThird(params, signHeaders)

基础的第三方登录方法，需要手动提供所有参数。

**参数：**
- `params` (Object): 登录参数
  - `tenantCode` (string): 租户代码
  - `account` (string): 账户
  - `scope` (string): 作用域
- `signHeaders` (Object): 签名头部信息
  - `vtxSignAppKey` (string): 应用密钥
  - `vtxSignTimestamp` (string): 时间戳
  - `vtxSign` (string): 签名

**返回：** Promise<Object> - 登录响应结果

### 2. loginByThirdAuto(params, signConfig)

便捷的第三方登录方法，自动生成时间戳。

**参数：**
- `params` (Object): 登录参数（同上）
- `signConfig` (Object): 签名配置
  - `appKey` (string): 应用密钥
  - `sign` (string): 签名

**返回：** Promise<Object> - 登录响应结果

### 3. generateTimestamp()

生成当前时间戳的工具方法。

**返回：** string - 当前时间戳

## 使用示例

### 基础用法

```javascript
import { loginByThird, generateTimestamp } from '@/api/thirdParty/login'

const loginParams = {
  tenantCode: 'your_tenant_code',
  account: 'your_account',
  scope: 'your_scope'
};

const signHeaders = {
  vtxSignAppKey: 'your_app_key',
  vtxSignTimestamp: generateTimestamp(),
  vtxSign: 'your_generated_sign'
};

try {
  const response = await loginByThird(loginParams, signHeaders);
  console.log('登录成功:', response);
} catch (error) {
  console.error('登录失败:', error);
}
```

### 便捷用法

```javascript
import { loginByThirdAuto } from '@/api/thirdParty/login'

const loginParams = {
  tenantCode: 'your_tenant_code',
  account: 'your_account',
  scope: 'your_scope'
};

const signConfig = {
  appKey: 'your_app_key',
  sign: 'your_generated_sign'
};

try {
  const response = await loginByThirdAuto(loginParams, signConfig);
  console.log('登录成功:', response);
} catch (error) {
  console.error('登录失败:', error);
}
```

### 在Vue组件中使用

```javascript
// 在组件的methods中
async handleLogin() {
  try {
    this.$loading = this.$loading({
      lock: true,
      text: '正在登录...'
    });

    const response = await loginByThirdAuto({
      tenantCode: this.form.tenantCode,
      account: this.form.account,
      scope: this.form.scope
    }, {
      appKey: this.config.appKey,
      sign: this.generateSign() // 需要实现签名生成逻辑
    });

    if (response && response.token) {
      this.$store.dispatch('user/setToken', response.token);
      this.$message.success('登录成功！');
      this.$router.push('/dashboard');
    }
  } catch (error) {
    this.$message.error(`登录失败: ${error.message}`);
  } finally {
    this.$loading.close();
  }
}
```

## 对应的curl命令

本接口封装对应以下curl命令：

```bash
curl --location --request POST 'http://ljfl.xzzfj.jinhua.gov.cn/cloud/management/api/v101/login/loginByThird' \
--header 'Content-Type: application/x-www-form-urlencoded' \
--header 'vtxSignAppKey=<vtxSignAppKey>' \
--header 'vtxSignTimestamp=<vtxSignTimestamp>' \
--header 'vtxSign=<vtxSign>' \
--data-urlencode 'tenantCode=<tenantCode>' \
--data-urlencode 'account=<account>' \
--data-urlencode 'scope=<scope>'
```

## 注意事项

1. **签名生成**: `vtxSign` 参数需要根据第三方接口的签名算法生成，请参考第三方接口文档实现签名逻辑。

2. **时间戳**: `vtxSignTimestamp` 建议使用当前时间戳，可以使用 `generateTimestamp()` 方法生成。

3. **错误处理**: 接口会抛出包含详细错误信息的异常，请做好错误处理。

4. **网络配置**: 如果在开发环境中遇到跨域问题，可能需要在 `vue.config.js` 中配置代理。

## 错误处理

接口会返回包含以下信息的错误对象：

```javascript
{
  status: 500,           // HTTP状态码
  message: '错误信息',    // 错误描述
  url: '请求URL',        // 请求的URL
  data: {...}           // 请求的数据
}
```

## 扩展和自定义

如果需要修改请求配置或添加其他功能，可以：

1. 修改 `src/utils/request.js` 中的 `requestThirdParty` 函数
2. 在 `src/api/thirdParty/login.js` 中添加新的方法
3. 参考 `src/examples/thirdPartyLoginExample.js` 中的示例进行扩展

## 相关文件

- `src/utils/request.js` - 请求工具函数
- `src/api/thirdParty/login.js` - 第三方登录接口
- `src/api/indexApi/index.js` - API统一导出
- `src/examples/thirdPartyLoginExample.js` - 使用示例
