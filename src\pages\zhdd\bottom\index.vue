<template>
  <div class='bottom' style='height: 600px'>
    <CommonTitle text='舆情中心'>
      <div class='zhddTitleBar'>
        <div class="buttons" @click="openDdsj">调度事件</div>
      </div>
    </CommonTitle>
    <div class="table_box">
      <div class="table">
        <div
          class="tbody"
          id="box0"
          @mouseenter="mouseenterEvent"
          @mouseleave="mouseleaveEvent"
        >
          <div
            class="tr"
            v-for="(item ,i) in zfdt"
            @click="showYqzxDetail(item)"
            :class="item.isLight==1?'tr_light':''"
          >
            <div class="text" :style="{color:item.state==0?'yellow':''}">
              {{item.text}}
            </div>
            <div class="time" style="white-space: nowrap">
              <div>
                  <span class="icon-zy"></span
                  ><span>{{item.source || '腾讯网'}}</span>
              </div>
              <div>
                  <span class="icon-dw"></span
                  ><span
              >{{item.dept_name.split(',')[0]&&item.dept_name.split(',')[0]||'-'}}</span
              >
                <span class="icon-sj" style="margin-left: 40px"></span
                ><span>{{item.time}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <bottomDialog :visible='visible' :yqMsg='yqMsg' @close='visible = false' @updateList='initApi(city)'></bottomDialog>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { indexApi } from '@/api/indexApi'
import bottomDialog from './bottomDialog'
import { getAdminToken } from '@/api/zhdd'
export default {
  name: 'index',
  components: {
    CommonTitle,
    bottomDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      zfdt: [],
      dom1: null,
      time1: null,

      yqMsg: {},
      visible: false
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.initApi(localStorage.getItem("city"),year);
    })
    this.$bus.$on('glnr', (e) => {
      if (e.data && e.data.glid) {
        this.glChange(e.data.glid);
      }
    })
    this.$bus.$on('updateYqList', (e) => {
      this.initApi(localStorage.getItem("city"));
    })
    this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
    // 表格滚动
    this.dom1 = document.getElementById("box0");
    this.mouseleaveEvent();
  },
  methods: {
    initApi(city) {
      let this_ = this;
      indexApi("/xzzf_qylb", { qx: city == "金华市" ? "" : city }).then(
        (res) => {
          this_.zfdt = res.data;
        }
      );
    },
    // 关联任务
    glChange(id) {
      let findI = this.zfdt.findIndex((a) => a.id == id);
      if (findI != -1) {
        if (this.glid != id) {
          this.glid = id;
          this.zfdt.forEach((item) => {
            if (item.id == id) {
              item.isLight = 1;
            } else {
              item.isLight = 0;
            }
          });
          this.zfdt.sort(function (a, b) {
            return b.isLight - a.isLight;
          });
          this.dom1.scrollTop = 0;
          this.mouseenterEvent();
        } else {
          this.glid = null;
          this.zfdt.forEach((item) => {
            item.isLight = 0;
          });
          this.$forceUpdate();
          this.mouseleaveEvent();
        }
      } else {
        this.$message({
          message: "该任务未关联舆情信息",
          type: "warning",
        });
      }
    },
    mouseenterEvent() {
      clearInterval(this.time1);
    },
    mouseleaveEvent() {
      this.time1 = setInterval(() => {
        this.dom1.scrollBy({
          top: (0, 80),
          behavior: "smooth",
        });
        if (
          this.dom1.scrollTop >=
          this.dom1.scrollHeight - this.dom1.offsetHeight
        ) {
          this.dom1.scrollTop = 0;
        }
      }, 1500);
    },
    openDdsj() {
      getAdminToken({}).then(res => {
        let token = res.data.msg;
        window.cookieStore.set("YGF-Token", token);
        window.open(
          "https://csdn.dsjj.jinhua.gov.cn:8303/direct/direction"
        );
      });
    },
    showYqzxDetail(item) {
      this.$bus.$emit('glid',item.id)
      this.yqMsg = item;
      this.visible = true;
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
  .zhddTitleBar {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .buttons {
      width: 236px;
      height: 80px;
      background: url("@/assets/zhdd/btn.png");
      background-size: 100% 100%;
      color: #9fc9e9;
      cursor: pointer;
      font-size: 28px;
      text-align: center;
    }
  }

  /* 表格样式================================== */
  .table_box {
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    height: 600px;
    overflow: hidden;
  }

  .table {
    width: 100%;
    height: 100%;
    /* padding: 10px; */
    box-sizing: border-box;
    position: relative;
  }

  .table .tbody {
    width: 100%;
    height: 100%;
    /* overflow-y: auto; */
    overflow: hidden;
  }

  .table .tbody:hover {
    overflow-y: auto;
  }

  .table .tbody::-webkit-scrollbar {
    width: 4px;
    /*滚动条整体样式*/
    height: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
  }

  .table .tbody::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }


  .table .tr {
    height: 136px;
    margin-top: 10px;
    cursor: pointer;
    background: url('@/assets/zhdd/bg.png') no-repeat;
    padding-top: 30px;
    box-sizing: border-box;
  }

  .icon-sj {
    display: inline-block;
    background: url('@/assets/zhdd/shijian.png') no-repeat;
    width: 31px;
    height: 30px;
    margin-right: 10px;
    vertical-align: middle;
  }

  .icon-zy {
    display: inline-block;
    background: url('@/assets/zhdd/ziyuan.png') no-repeat;
    width: 31px;
    height: 30px;
    margin-right: 10px;
    vertical-align: middle;
  }

  .table .time {
    font-size: 28px;
    font-weight: 400;
    color: #77B3F1;
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
  }

  .table .text {
    font-size: 30px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #fff;
    text-align: left;
  }

  /* 表格自动滚动 */
  @keyframes rowUp {
    0% {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }

    100% {
      transform: translate3d(0, -100%, 0);
      -webkit-transform: translate3d(0, -100%, 0);
      -moz-transform: translate3d(0, -100%, 0);
      -ms-transform: translate3d(0, -100%, 0);
      -o-transform: translate3d(0, -100%, 0);
    }
  }

  /* .tr {
    animation: 10s rowUp linear infinite normal;
    -webkit-animation: 10s rowUp linear infinite normal;
  } */

  .tbody:hover .tr {
    animation-play-state: paused;
  }
</style>