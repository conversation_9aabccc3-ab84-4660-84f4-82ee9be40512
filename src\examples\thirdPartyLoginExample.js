/**
 * 第三方登录使用示例
 * 这个文件展示了如何在项目中使用第三方登录接口
 */

import { loginByThird, loginByThirdAuto, generateTimestamp } from '@/api/thirdParty/login'
import { setToken } from '@/utils/auth'

/**
 * 示例1：基础第三方登录
 * 手动指定所有参数的登录方式
 */
export async function basicThirdPartyLogin() {
  try {
    const loginParams = {
      tenantCode: 'your_tenant_code',    // 替换为实际的租户代码
      account: 'your_account',           // 替换为实际的账户
      scope: 'your_scope'                // 替换为实际的作用域
    };

    const signHeaders = {
      vtxSignAppKey: 'your_app_key',     // 替换为实际的应用密钥
      vtxSignTimestamp: generateTimestamp(), // 生成当前时间戳
      vtxSign: 'your_generated_sign'     // 替换为根据签名算法生成的签名
    };

    const response = await loginByThird(loginParams, signHeaders);
    
    if (response && response.token) {
      // 登录成功，保存token
      setToken(response.token);
      console.log('第三方登录成功:', response);
      return response;
    } else {
      throw new Error('登录响应中没有token');
    }
  } catch (error) {
    console.error('第三方登录失败:', error);
    throw error;
  }
}

/**
 * 示例2：便捷第三方登录
 * 使用自动生成时间戳的便捷方法
 */
export async function autoThirdPartyLogin() {
  try {
    const loginParams = {
      tenantCode: 'your_tenant_code',
      account: 'your_account',
      scope: 'your_scope'
    };

    const signConfig = {
      appKey: 'your_app_key',
      sign: 'your_generated_sign'  // 需要根据具体的签名算法生成
    };

    const response = await loginByThirdAuto(loginParams, signConfig);
    
    if (response && response.token) {
      setToken(response.token);
      console.log('第三方登录成功:', response);
      return response;
    } else {
      throw new Error('登录响应中没有token');
    }
  } catch (error) {
    console.error('第三方登录失败:', error);
    throw error;
  }
}

/**
 * 示例3：在Vue组件中使用第三方登录
 * 这是一个可以在Vue组件中使用的方法
 */
export const thirdPartyLoginMixin = {
  methods: {
    async handleThirdPartyLogin(loginData) {
      try {
        // 显示加载状态
        this.$loading = this.$loading({
          lock: true,
          text: '正在进行第三方登录...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        const { tenantCode, account, scope, appKey, sign } = loginData;

        const response = await loginByThirdAuto(
          { tenantCode, account, scope },
          { appKey, sign }
        );

        if (response && response.token) {
          setToken(response.token);
          this.$message.success('第三方登录成功！');
          
          // 跳转到主页或指定页面
          this.$router.push('/home');
          
          return response;
        } else {
          throw new Error('登录失败，请检查参数');
        }
      } catch (error) {
        console.error('第三方登录错误:', error);
        this.$message.error(`登录失败: ${error.message || '未知错误'}`);
        throw error;
      } finally {
        // 隐藏加载状态
        if (this.$loading) {
          this.$loading.close();
        }
      }
    }
  }
};

/**
 * 示例4：签名生成工具函数
 * 注意：这里只是示例，实际的签名算法需要根据第三方接口文档实现
 */
export function generateSign(params, appSecret, timestamp) {
  // 这里需要根据实际的签名算法实现
  // 通常的步骤：
  // 1. 将参数按字典序排序
  // 2. 拼接成字符串
  // 3. 加上appSecret和timestamp
  // 4. 进行MD5或SHA256等加密
  
  // 示例实现（需要根据实际情况修改）:
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  const signString = `${sortedParams}&appSecret=${appSecret}&timestamp=${timestamp}`;
  
  // 这里应该使用实际的加密算法
  // 例如：return md5(signString) 或 return sha256(signString)
  console.warn('请实现实际的签名算法');
  return 'placeholder_sign'; // 占位符，需要替换为实际签名
}

/**
 * 示例5：完整的登录流程
 * 包含参数验证、签名生成、登录请求等完整流程
 */
export async function completeThirdPartyLogin(config) {
  const { tenantCode, account, scope, appKey, appSecret } = config;
  
  // 参数验证
  if (!tenantCode || !account || !scope || !appKey || !appSecret) {
    throw new Error('缺少必要的登录参数');
  }
  
  try {
    // 生成时间戳
    const timestamp = generateTimestamp();
    
    // 生成签名
    const loginParams = { tenantCode, account, scope };
    const sign = generateSign(loginParams, appSecret, timestamp);
    
    // 执行登录
    const response = await loginByThird(loginParams, {
      vtxSignAppKey: appKey,
      vtxSignTimestamp: timestamp,
      vtxSign: sign
    });
    
    return response;
  } catch (error) {
    console.error('完整登录流程失败:', error);
    throw error;
  }
}

/**
 * 使用说明：
 * 
 * 1. 在Vue组件中使用：
 *    import { thirdPartyLoginMixin } from '@/examples/thirdPartyLoginExample'
 *    export default {
 *      mixins: [thirdPartyLoginMixin],
 *      methods: {
 *        async login() {
 *          await this.handleThirdPartyLogin({
 *            tenantCode: 'xxx',
 *            account: 'xxx',
 *            scope: 'xxx',
 *            appKey: 'xxx',
 *            sign: 'xxx'
 *          });
 *        }
 *      }
 *    }
 * 
 * 2. 在普通JS中使用：
 *    import { basicThirdPartyLogin } from '@/examples/thirdPartyLoginExample'
 *    basicThirdPartyLogin().then(response => {
 *      console.log('登录成功', response);
 *    });
 * 
 * 3. 自定义签名算法：
 *    修改 generateSign 函数，实现符合第三方接口要求的签名算法
 */
