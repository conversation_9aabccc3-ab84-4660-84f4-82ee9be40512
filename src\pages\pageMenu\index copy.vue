<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-06-19 08:32:42
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-19 08:32:42
-->
<template>
  <div class="choosePage" id="choosePage">
    <div class="name">
      {{ city + '城市运行管理服务系统' }}
    </div>
    <div class="tops">
      <div
        class="topItem"
        v-for="(item, i) in topArr"
        :key="i"
        @click="pageJump(item)"
        :style="{ background: 'url(' + item.back + ')' }"
      ></div>
    </div>
    <div class="pages">
      <div
        class="pageItem"
        v-for="(item, i) in pageArr"
        :key="i"
        :style="{ background: 'url(' + item.img + ')' }"
        @click="pageJump(item)"
      >
        <div class="pageName">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrl, indexApi, getSzcjToken, getUser<PERSON><PERSON>, loginByThirdComplete } from '@/api/indexApi'
import mainObj from '@/utils/PocMain.js'
import { Encrypt } from '@/utils/Encrypt'

export default {
  name: 'index',
  data() {
    return {
      topArr: [
        {
          back: require('@/assets/login/login_csyxgl.png'),
          name: '首页',
          key: 'csyxgl',
        },
        {
          back: require('@/assets/login/login_admin.png'),
          name: '后台管理',
        },
      ],
      pageArr: [
        {
          img: require('@/assets/login/login_ssyj.png'),
          name: '三色预警一张图',
          key: 'ssyj',
        },
        {
          img: require('@/assets/login/login_xxzf_zhpj.png'),
          name: '行政执法综合评价一张图',
          key: 'xxzf_zhpj',
        },
        {
          img: require('@/assets/login/login_csgl.png'),
          name: '城市管理一张图',
          key: 'csgl',
        },
        {
          img: require('@/assets/login/login_wlwsb.png'),
          name: '物联网设备一张图',
          key: 'wlwsb',
        },
        {
          img: require('@/assets/login/login_ljfl.png'),
          name: '垃圾分类一张图',
          key: 'ljfl',
        },
        {
          img: require('@/assets/login/login_gzfw.png'),
          name: '公众服务一张图',
          key: 'gzfw',
        },
        {
          img: require('@/assets/login/login_ajhf.png'),
          name: '案件回访',
          key: 'ajhf',
        },
        {
          img: require('@/assets/login/login_yyjc.png'),
          name: '应用集成',
          key: 'yyjc',
        },
        // {
        //   img: require('@/assets/login/login_jxpg.png'),
        //   name: '绩效评估',
        //   key: 'jxpg',
        // },
        {
          img: require('@/assets/login/login_zhpj.png'),
          name: '综合评价一张图',
          key: 'zhpj',
        },
        {
          img: require('@/assets/login/login_yxjc.png'),
          name: '运行监测一张图',
          key: 'yxjc',
        },
        {
          img: require('@/assets/login/login_qlgl.png'),
          name: '犬类管理一张图',
          key: 'dog',
        },
        {
          img: require('@/assets/login/login_zqzf.png'),
          name: '站前执法一张图',
          key: 'zqyzt',
        },
        {
          img: require('@/assets/login/login_szcj.png'),
          name: '数字城建一张图',
          key: 'szcj',
        },
        {
          img: require('@/assets/login/login_xzzf.png'),
          name: '行政执法一张图',
          key: 'home',
        },
        {
          img: require('@/assets/login/login_zfts.png'),
          name: '执法态势一张图',
          key: 'zfts',
        },
        {
          img: require('@/assets/login/login_zhdd.png'),
          name: '指挥调度一张图',
          key: 'zhdd',
        },
        {
          img: require('@/assets/login/login_xjzx.png'),
          name: '县级中心',
          key: 'xjzx',
        },
      ],
      szcjToken: '',
    }
  },
  computed: {
    city() {
      return localStorage.getItem('adminCity')
    },
  },
  mounted() {
    // 初始化 POC

    try {
      mainObj.init()

      // 然后初始化视频数据
      this.initPocVideoData()
    } catch (error) {
      console.error('POC 初始化失败:', error)
    }
    // this.getPageMenu()
    this.getArea()
    this.getSzcjToken()
  },
  methods: {
    //获取用户所属区县
    getArea() {
      getUserArea().then((res) => {
        if (res.code == 200) {
          localStorage.setItem('city', res.data.area)
          localStorage.setItem('adminCity', res.data.area)
        }
      })
    },
    async initPocVideoData() {
      try {
        const authInfo = await mainObj.queryAuth()
        console.log('POC 认证信息:', authInfo)

        // 继续处理视频数据...
      } catch (error) {
        console.error('初始化 POC 视频数据失败:', error)
      }
    },
    getPageMenu() {
      indexApi('/xzzfj_sy_cd', { area: localStorage.getItem('adminCity') }).then((res) => {
        this.pageArr = res.data
      })
    },
    getSzcjToken() {
      getSzcjToken({
        username: '13735713555',
        password: Encrypt('%&fyAB2%mZ'),
      }).then((res) => {
        console.log('szcjToken', res.token)
        this.szcjToken = res.token
      })
    },
    pageJump(item) {
      switch (item.name) {
        case '绩效评估':
          getUrl('/token/getTokenInfo', { jmppage: 'ks' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '三色预警一张图':
          getUrl({ type: 'dashboard', module: 'ssyj' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '行政执法综合评价一张图':
          getUrl({ type: 'dashboard', module: 'xzzfpjzb' }).then((res) => {
            if (res.code == 200) {
              this.openHtmlByMode(res.data.url)
            }
          })
          break
        case '首页':
          this.getPage(item)
          break
        case '驾驶舱':
          this.getPage(item)
          break
        case '指挥调度一张图':
          this.getPage(item)
          break
        case '执法态势一张图':
          this.getPage(item)
          break
        case '行政执法一张图':
          this.getPage(item)
          break
        case '城市管理一张图':
          this.getPage(item)
          break
        case '物联网设备一张图':
          this.getPage(item)
          break
        case '公众服务一张图':
          this.getPage(item)
          break
        case '综合评价一张图':
          this.getPage(item)
          break
        case '运行监测一张图':
          this.getPage(item)
          break
        case '犬类管理一张图':
          this.getPage(item)
          break
        case '站前执法一张图':
          this.getPage(item)
          break
        case '县级中心':
          this.getPage(item)
          break
        case '应用集成':
          this.getPage(item)
          break
        case '后台管理':
          this.openHtmlByMode(
            process.env.NODE_ENV === 'production'
              ? 'http://10.45.13.116/ygf/login'
              : 'http://10.45.13.116:8000/ygf-web/login'
          )
          break
        case '案件回访':
          this.getPage(item)
          break
        case '数字城建一张图':
          this.openHtmlByMode('https://jhqy.jsj.jinhua.gov.cn/dplus/view/1672878871176626178?token=' + this.szcjToken)
        case '垃圾分类一张图':
          loginByThirdComplete({
            tenantCode: 'TENANT_JHCC',
            account: 'dddl',
            scope: 'THIRD_APP',
            appKey: '25jhljfl05ygf29',
            appSecret: 'B7FEAAEAD3AD3ED37468A45EF8030E94',
          }).then((res) => {
            if (res.msg == '登录成功') {
              if (res.data.access_token) {
                let access_token = res.data.access_token
                this.openHtmlByMode(`http://ljfl.xzzfj.jinhua.gov.cn/#/autoLogin?token=${access_token}`)
              }
            }
          })
          break
      }
    },
    getPage(item) {
      this.$router.push('/' + item.key)
    },
    openHtmlByMode(url) {
      window.open(url)
    },
    //获取端口号
    getCurrentPortWithDefault() {
      let port = window.location.port
      if (port === '') {
        if (window.location.protocol === 'http:') {
          port = '80'
        } else if (window.location.protocol === 'https:') {
          port = '443'
        }
      }
      return port
    },
  },
  watch: {},
}
</script>

<style scoped>
.choosePage {
  width: 100%;
  height: 100%;
  background: url('@/assets/login/bg.png');
  background-size: cover;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.name {
  width: 100%;
  text-align: center;
  font-size: 64px;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  color: #e3f3ff;
  margin-top: 70px;
  margin-left: 20px;
}

.pages {
  width: 3480px;
  height: 878px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 150px;
}

.pageItem {
  width: 307px;
  height: 561px;
  display: flex;
  justify-content: center;
  cursor: pointer;
  background-size: 100% 100% !important;
  margin: 0 32px 100px;
}

.pageName {
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 52px;
  color: #e3f3ff;
  line-height: 54px;
  margin-top: 420px;
  width: 200px;
  text-align: center;
}

.tops {
  width: 100%;
  height: fit-content;
  padding: 0 171px;
  box-sizing: border-box;
  display: flex;
  justify-content: right;
  margin-top: 50px;
}
.topItem {
  width: 420px;
  height: 172px;
  margin-left: 80px;
  cursor: pointer;
  background-size: 100% 100% !important;
}
@font-face {
  font-family: YouSheBiaoTiHei;
  src: url('@/assets/fonts/YouSheBiaoTiHei-2.ttf');
  font-weight: normal;
  font-style: normal;
}
</style>
