<template>
  <div>
    <CommonTitle text='回访进度'></CommonTitle>
    <div class="hfjd-container">
      <div style="position: absolute; top: 940px; left: 680px; z-index: 2" v-show="year == $currentYear" class='yearChange'>
        <el-date-picker
          v-model="value2"
          type="monthrange"
          @change="(range) => getHfjd(range,city,year)"
          range-separator="-"
          start-placeholder="开始月份"
          value-format="yyyy-MM"
          end-placeholder="结束月份"
          :append-to-body='false'>
        </el-date-picker>
      </div>
      <div class="hfjdCharts" id="hfjdCharts"></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import moment from 'moment'
import { getHfjdList } from '@/api/ajhf'
import { registerCityYearChangeEvents } from '@/utils'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      value2: [
        new Date().getFullYear() + "-01-01",
        moment(new Date()).format("YYYY-MM-DD"),
      ],
    }
  },
  computed: {},
  mounted() {
    registerCityYearChangeEvents(this,this.initApi)
  },
  methods: {
    initApi(city,year) {
      this.getHfjd(this.value2, city);
    },
    //获取回访进度信息
    getHfjd(range, city, year) {
      getHfjdList({
        xsq: city,
        startTime: range[0],
        endTime: range[1],
      }).then(res => {
        if (res.code == 200) {
          this.currentHfjdData = res.data
          setTimeout(() => {
            this.initHfjdCharts(this.currentHfjdData)
          },300)
        }
      })
    },
    initHfjdCharts(currentHfjdData) {
      let myChart = this.$echarts.init(document.getElementById("hfjdCharts"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            return params.find(item => item.seriesName != "背景").axisValue + ": " + params.find(item => item
              .seriesName != "背景").value + "%";
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        grid: {
          top: "5%",
          left: "5%",
          right: "12%",
          bottom: "0",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          name: "",
          type: "category",
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
            length: 10,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#FFFFFF",
              fontSize: 32,
            },
          },
          data: currentHfjdData.map(item => item.xsq),
        },
        series: [{
          type: "bar",
          name: "",
          showBackground: true,
          backgroundStyle: {
            color: 'transparent'
          },
          itemStyle: {
            normal: {
              barBorderRadius: 30,
              color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 1, [{
                offset: 0,
                color: "rgba(60,253,255,0.2)",
              },
                {
                  offset: 1,
                  color: "#3CFDFF",
                },
              ]),
            }
          },
          label: {
            show: true,
            position: [760, -3],
            color: "#fff",
            formatter: function (params) {
              return params.value + "%";
            },
            fontSize: 32,
            textStyle: {
              color: '#FFFFFF',
              fontWeight: 'bold',
              fontFamily: 'Source Han Sans CN'
            }
          },
          barWidth: 20,
          color: "#539FF7",
          data: currentHfjdData.map(item => item.jd),
        }, {
          name: "背景",
          type: "bar",
          barWidth: 20,
          barGap: "-100%",
          data: currentHfjdData.map(item => 100),
          itemStyle: {
            normal: {
              barBorderRadius: 30,
              color: "#094471",
            },
          },
          z: 0,
        }]
      }
      myChart.setOption(option)
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.hfjd-container {
  width: 100%;
  height: 1010px;
  /deep/ .yearChange {
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
    }
  }
}

.hfjdCharts {
  width: 100%;
  height: 800px;
}
</style>