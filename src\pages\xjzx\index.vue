<template>
  <div class="container" id="bmjr-main">
    <div class="selectContainer" v-show="city == '金华市'">
      <el-select
        class="citySel"
        v-model="cityValue"
        placeholder="请选择"
        @change="cityChange"
      >
        <el-option
          v-for="item in cityOptions"
          :key="item.value"
          :label="item.label"
          :value="item.label"
        >
        </el-option>
      </el-select>
    </div>
    <div class="center">
      <div
        class="s-flex-col s-col-center"
        style="margin-top: 325px; line-height: 170px"
      >
        <span class="s-w7 s-c-blue-gradient">{{total}}</span>
        <span class="s-w7 s-font-80 s-c-white">总应用数</span>
      </div>
    </div>
    <div class="box">
      <el-carousel
        indicator-position="outside"
        height="700px"
        :autoplay="false"
      >
        <el-carousel-item v-for="(el,i) in newYyjcList" :key="i">
          <div class="yyjc_left">
            <div
              class="yyjc_item"
              v-for="(item,index) in newYyjcList[i].slice(0,6)"
              @click="xjzxItemClick(item)"
            >
              <img :src="getImage(item.name)"
                alt="" width="379px" height="213px" />
              <div class="yyjc_item_title">{{item.name}}</div>
            </div>
          </div>
          <div class="yyjc_right">
            <div
              class="yyjc_item"
              v-for="(item,index) in newYyjcList[i].slice(6)"
              @click="xjzxItemClick(item)"
            >
              <img :src="getImage(item.name)"
                alt="" width="379px" height="213px" />
              <div class="yyjc_item_title">{{item.name}}</div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
import { getPalink, getUrl, getYklink } from '@/api/home'
import { mapActions } from 'vuex'
export default {
  name: 'index',
  data() {
    return {
      total: null,
      yyjcListAll: [],
      yyjcList: [],
      cityValue: "金华市",
      city: localStorage.getItem('adminCity'),
      cityOptions: [
        { value: "1", label: "金华市" },
        { value: "2", label: "婺城区" },
        { value: "3", label: "金东区" },
        { value: "4", label: "兰溪市" },
        { value: "5", label: "东阳市" },
        { value: "6", label: "义乌市" },
        { value: "7", label: "永康市" },
        { value: "8", label: "武义县" },
        { value: "9", label: "浦江县" },
        { value: "10", label: "磐安县" },
        { value: "11", label: "开发区" },
      ],
      departmentVal: "全部门",
      department: [
        { value: "1", label: "全部门" },
        { value: "2", label: "办公室" },
      ],
    }
  },
  computed: {
    newYyjcList() {
      let newArr = [];
      for (let i = 0; i < this.yyjcList.length; i += 12) {
        newArr.push(this.yyjcList.slice(i, i + 12));
      }
      return newArr;
    },
  },
  mounted() {
    this.initApi(localStorage.getItem("adminCity"));
  },
  methods: {
    ...mapActions('common', ['updateActiveCityName']),
    initApi(city,year) {
      //请求data文件夹下xjzx.json文件
      fetch('/jsc/xjzx.json').then(response => response.json()).then(res => {
        if (city == '金华市') {
          this.yyjcListAll = res.data;
        } else {
          this.yyjcListAll = res.data.filter(item => item.city == city)
        }
        this.cityChange(city);
      })
    },
    getImage(name) {
      const requireContext = require.context('@/assets/yyjc/', false, /\.png$/);
      const defaultImage = 'tmp.gif';
      const excludedNames = ['浦江县防违控违综合信息管理系统'];
      const imagePath = excludedNames.includes(name) ? defaultImage : `${name}.png`;
      if (requireContext.keys().includes(`./${imagePath}`)) {
        return requireContext(`./${imagePath}`);
      } else {
        console.warn(`Image not found: ${imagePath}`);
        return requireContext(`./${defaultImage}`);
      }
    },
    xjzxItemClick(el) {
      if (el.key) {
        this.updateActiveCityName(el.city);
        this.$bus.$emit("cityChange", el.city);
        this.$router.push("/home")
      } else if (el.isDddl) {
        //是否单点登录
        if (el.name == "磐安县行政执法指挥中心") {
          getPalink(JSON.stringify({
            appId: "fc06313f1c824351ab855604366f27bc",
            appSecret: "d83f902a2e344c41bdd3e3a7a8eea9f3",
            timeStamp: Date.now(),
            sign: md5("appId=fc06313f1c824351ab855604366f27bc&appSecret=d83f902a2e344c41bdd3e3a7a8eea9f3&timeStamp=" + Date.now() + "&key=message").toUpperCase(),
          })).then(res => {
            console.log(res.data.token);
            window.open(window.location.origin + "/paxmd/sso/login?token=" + res.data.token + "&userName=pazhzx&loginType=CABIN")
          })
        }
      } else {
        this.openWebWin(el)
      }
    },
    cityChange(e) {
      if (e == "金华市") {
        this.yyjcList = this.yyjcListAll;
      } else {
        this.yyjcList = this.yyjcListAll.filter((a) => a.city == e);
      }
      this.total = this.yyjcList.length;
    },
    openWebWin(item) {
      let that = this;
      let name = item.name;
      let url = item.url;
      if (url == "") {
        this.$message.error("暂无该大屏信息");
        return false;
      }

      if (url == "currentUrlProject") {
        getUrl(item.city).then(res => {
          top.window.location = res.data.data.url
        })
      } else if (name == "金华永康市永城数治系统") {
        // 金华永康市永城数治系统
        getYklink({appId: "330700_tsapp_056",userId: ''}).then(res => {
          if (res.code === 200) {
            that.openHtmlByMode(
              url + "&csdnCode=" + res.tempAuthCode,
              3840,
              2160
            );
          } else {
            this.$message.error("暂无权限");
          }
        })
      } else {
        let type = url.substring(0, 5).indexOf("s") != -1 ? true : false;
        that.openHtmlByMode(url, 3840, 2160);
      }
    },
    openHtmlByMode(url, width, higth) {
      let moveLeft = (7680 - width) / 2;
      let moveHigth = (2160 - higth) / 2;
      window.open(
        url,
        "项目接入系统",
        "directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=" +
        higth +
        ", width=" +
        width +
        ", top=" +
        moveHigth +
        ", left=" +
        moveLeft +
        ""
      );
    },
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.container {
  position: relative;
  width: 3840px;
  height: 2160px;
  background: url("@/assets/yyjc/yyjc2_bg.png") no-repeat;
  background-size: cover;
  transform: translateX(0px);
  z-index: 10;
  /deep/ .selectContainer {
    /* 下拉 */
    .el-input__inner {
      height: 80px !important;
      border: 1px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 10px !important;
      font-style: unset !important;
      font-size: 30px !important;

      font-weight: normal !important;
      background: #132c4e !important;
      -webkit-background-clip: unset !important;
      -webkit-text-fill-color: unset !important;
    }
    .el-select__caret {
      font-size: 25px !important;
      position: relative !important;
      right: 40px !important;
    }
    .el-input {
      width: 300px !important;
      font-size: 30px !important;
    }
  }

  /deep/ .box {
    position: absolute;
    top: 20%;
    width: 100% !important;
    height: 88% !important;
    .el-carousel__button {
      width: 10px !important;
      height: 10px !important;
      border-radius: 50% !important;
    }
    .el-carousel__arrow {
      width: 50px !important;
      height: 50px !important;
      font-size: 30px !important;
      background-color: rgba(31, 45, 61, 0.5) !important;
    }
    .el-carousel--horizontal,
    .el-carousel__container {
      width: 100% !important;
      height: 88% !important;
    }
    .el-carousel__indicator .el-carousel__button {
      width: 80px !important;
      height: 20px !important;
      background: #36bfff !important;
      border-radius: 10px !important;
    }
    .el-carousel__indicator--horizontal {
      padding: 12px !important;
    }
    .el-carousel__indicators--outside button {
      background-color: #36bfff !important;
    }
  }
}

/deep/ #app {
  .el-select-dropdown {
    background-color: #132c4e !important;
    border: 1px solid #afdcfb !important;
  }
  .el-select-dropdown__item {
    font-size: 30px !important;
    height: 50px !important;
    color: #cfcfd6 !important;
    line-height: 50px !important;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #27508f !important;
  }
}

.center {
  font-size: 223px;
  position: absolute;
  left: calc(50% - 638px);
  top: 15%;
  width: 1285px;
  height: 1082px;
  background: url("@/assets/yyjc/yyjc2_cen.png") no-repeat;
  background-size: cover;
}
.center div > span {
  font-family: DINCondensed;
}
.yyjc_left,
.yyjc_right {
  max-width: 1050px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.yyjc_left {
  position: absolute;
  left: 120px;
}
.yyjc_right {
  position: absolute;
  right: 120px;
  top: 20px;
}
.yyjc_item {
  font-style: italic;
  width: 450px;
  height: 400px;
  text-align: center;
  font-size: 32px;
  margin: 30px;
  color: #fff;
}
.citySel {
  position: absolute;
  top: 246px;
  left: 184px;
}
.departmentSel {
  position: absolute;
  top: 246px;
  left: 534px;
}
</style>