<template>
  <div class="container">
    <div class="center">
      <div
        class="s-flex-col s-col-center"
        style="margin-top: 325px; line-height: 170px"
      >
        <span class="s-w7 s-c-blue-gradient">{{total}}</span>
        <span class="s-w7 s-font-80 s-c-white">总应用数</span>
      </div>
    </div>
    <div class="box">
      <el-carousel
        indicator-position="outside"
        height="700px"
        :autoplay="false"
      >
        <el-carousel-item v-for="(el,i) in newYyjcList" :key="i">
          <div class="yyjc_left">
            <div
              class="yyjc_item"
              v-for="(item,index) in newYyjcList[i].slice(0,6)"
              @click="openWebWin(item)"
            >
              <img
                :src="item.img"
                alt="" width="379px" height="213px" />
              <div class="yyjc_item_title">{{item.name}}</div>
            </div>
          </div>
          <div class="yyjc_right">
            <div
              class="yyjc_item"
              v-for="(item,index) in newYyjcList[i].slice(6)"
              @click="openWebWin(item)"
            >
              <img
                :src="item.img"
                alt="" width="379px" height="213px" />
              <div class="yyjc_item_title">{{item.name}}</div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
import { indexApi } from '@/api/indexApi'
import { getUrl, getYklink } from '@/api/home'
export default {
  name: 'index',
  data() {
    return {
      total: null,
      yyjcList: [],
      city: localStorage.getItem('adminCity'),
      departmentVal: "全部门",
      department: [
        { value: "1", label: "全部门" },
        { value: "2", label: "办公室" },
      ],
    }
  },
  computed: {
    newYyjcList() {
      let newArr = [];
      for (let i = 0; i < this.yyjcList.length; i += 12) {
        newArr.push(this.yyjcList.slice(i, i + 12));
      }
      return newArr;
    },
  },
  mounted() {
    this.initApi(localStorage.getItem("city"));
  },
  methods: {
    initApi(city,year) {
      indexApi("/xzzfj_yyji_list", { area: city }).then(res => {
        console.log(res);
        this.total = res.data.length
        this.yyjcList = res.data.map(item => {return {
          name: item.name,
          url: item.url,
          city: item.area,
          img: this.getDownLoadUrl(item.picture_url)
        }})
      })
    },
    openWebWin(item) {
      let that = this;
      let name = item.name;
      let url = item.url;
      if (url == "") {
        this.$message.error("暂无该大屏信息");
        return false;
      }

      if (url == "currentUrlProject") {
        getUrl(item.city).then(res => {
          top.window.location = res.data.data.url
        })
      } else if (name == "金华永康市永城数治系统") {
        // 金华永康市永城数治系统
        getYklink({appId: "330700_tsapp_056",userId: ''}).then(res => {
          if (res.code === 200) {
            that.openHtmlByMode(
              url + "&csdnCode=" + res.tempAuthCode,
              3840,
              2160
            );
          } else {
            this.$message.error("暂无权限");
          }
        })
      } else {
        let type = url.substring(0, 5).indexOf("s") != -1 ? true : false;
        that.openHtmlByMode(url, 3840, 2160);
      }
    },
    openHtmlByMode(url, width, higth) {
      let moveLeft = (7680 - width) / 2;
      let moveHigth = (2160 - higth) / 2;
      window.open(
        url,
        "项目接入系统",
        "directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=" +
        higth +
        ", width=" +
        width +
        ", top=" +
        moveHigth +
        ", left=" +
        moveLeft +
        ""
      );
    },
    getDownLoadUrl(path) {
      return process.env.VUE_APP_BASE_API + '/sysUploadFile/downloadLocalFile?path=' + encodeURI(path)
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.container {
  position: relative;
  width: 3840px;
  height: 2160px;
  background: url("@/assets/yyjc/yyjc2_bg.png") no-repeat;
  background-size: cover;
  transform: translateX(0px);
  z-index: 10;
  /deep/ .selectContainer {
    /* 下拉 */
    .el-input__inner {
      height: 80px !important;
      border: 1px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 10px !important;
      font-style: unset !important;
      font-size: 30px !important;

      font-weight: normal !important;
      background: #132c4e !important;
      -webkit-background-clip: unset !important;
      -webkit-text-fill-color: unset !important;
    }
    .el-select__caret {
      font-size: 25px !important;
      position: relative !important;
      right: 40px !important;
    }
    .el-input {
      width: 300px !important;
      font-size: 30px !important;
    }
  }

  /deep/ .box {
    position: absolute;
    top: 20%;
    width: 100% !important;
    height: 88% !important;
    .el-carousel__button {
      width: 10px !important;
      height: 10px !important;
      border-radius: 50% !important;
    }
    .el-carousel__arrow {
      width: 50px !important;
      height: 50px !important;
      font-size: 30px !important;
      background-color: rgba(31, 45, 61, 0.5) !important;
    }
    .el-carousel--horizontal,
    .el-carousel__container {
      width: 100% !important;
      height: 88% !important;
    }
    .el-carousel__indicator .el-carousel__button {
      width: 80px !important;
      height: 20px !important;
      background: #36bfff !important;
      border-radius: 10px !important;
    }
    .el-carousel__indicator--horizontal {
      padding: 12px !important;
    }
    .el-carousel__indicators--outside button {
      background-color: #36bfff !important;
    }
  }
}

/deep/ #app {
  .el-select-dropdown {
    background-color: #132c4e !important;
    border: 1px solid #afdcfb !important;
  }
  .el-select-dropdown__item {
    font-size: 30px !important;
    height: 50px !important;
    color: #cfcfd6 !important;
    line-height: 50px !important;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #27508f !important;
  }
}

.center {
  font-size: 223px;
  position: absolute;
  left: calc(50% - 638px);
  top: 15%;
  width: 1285px;
  height: 1082px;
  background: url("@/assets/yyjc/yyjc2_cen.png") no-repeat;
  background-size: cover;
}
.center div > span {
  font-family: DINCondensed;
}
.yyjc_left,
.yyjc_right {
  max-width: 1050px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.yyjc_left {
  position: absolute;
  left: 120px;
}
.yyjc_right {
  position: absolute;
  right: 120px;
  top: 20px;
}
.yyjc_item {
  font-style: italic;
  width: 450px;
  height: 400px;
  text-align: center;
  font-size: 32px;
  margin: 30px;
  color: #fff;
}
.citySel {
  position: absolute;
  top: 246px;
  left: 184px;
}
.departmentSel {
  position: absolute;
  top: 246px;
  left: 534px;
}
</style>