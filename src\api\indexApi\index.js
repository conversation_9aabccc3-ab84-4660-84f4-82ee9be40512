import {request} from '@/utils/request'

// 指标接口
export function indexApi(id,params = {}) {
  return request({
    url: `/indexPort?indexid=${id}`,
    method: 'get',
    headers: {
      "Content-Type": "application/json;charset=utf-8",
      "ptid": "PT0001",
    },
    dataType: "json",
    params
  })
}

//登陆接口
export function login(data) {
  return request({
    url: `/jscLogin`,
    method: 'post',
    data
  })
}

// 获取用户所属区县
export function getUserArea() {
  return request({
    url: `/xzzfj/xzzfjZfsb/getArea`,
    dataType: "json",
    method: 'get',
  })
}

//免登获取账户信息
export function getUserInfo(params) {
  return request({
    url: `/token/authentication`,
    method: 'get',
    params
  })
}

//获取第三方url
export function getUrl(params) {
  return request({
    url: `/token/getTokenInfo`,
    method: 'get',
    params
  })
}

//数字城建token获取
export function getSzcjToken(data) {
  return request({
    url: `/szcjApi/zjsc/prod-api/loginForSC`,
    method: 'post',
    data
  })
}

//省厅免登
export function loginV4(params) {
  return request({
    url: `/loginV4`,
    method: 'get',
    params
  })
}