<template>
  <div>
    <div class="hearder_h1">
      <span v-for="(item,i) in Tabs" @click="currentTab = i" style="cursor: pointer;position: relative;bottom: 27px" class='titleText' :class="{commonTab: currentTab != i}">{{item}}</span>
    </div>
    <div class='yearBox'>
      <div style="position: absolute; top: 840px; left: 680px; z-index: 2" v-show="year == $currentYear" class='yearChange'>
        <el-date-picker
          v-model="value2"
          type="monthrange"
          @change="(range) => dateChange(range)"
          range-separator="-"
          start-placeholder="开始月份"
          value-format="yyyy-MM"
          end-placeholder="结束月份"
          :append-to-body='false'>
        </el-date-picker>
      </div>
    </div>
    <!--认领跟踪-->
    <div class="jbgz-container" v-show="currentTab == 1">
      <div class="jbgz-container-tabs">
        <div class="jbgz-container-tab" v-for="(item,i) in jbgzTabList" :key="i"
             :class="{jbgzContainerActiveTab: currentjbgz == i}" @click="jbgzTabClick(i,item)">
          {{ item.type }}
        </div>
      </div>
      <div class="jbgz-container-table">
        <div class="jbgz-container-table-container-line lineTitle">
          <div class="jbgz-container-table-container-line-column columnTitle">任务名称</div>
          <div class="jbgz-container-table-container-line-column columnTitle">接收部门</div>
          <div class="jbgz-container-table-container-line-column columnTitle">接收时间</div>
          <div class="jbgz-container-table-container-line-column columnTitle">处理状态</div>
        </div>
        <div class="jbgz-container-table-container">
          <div class="jbgz-container-table-container-line" v-for="(item,i) in tableData" :key="i"
               :class="{twiceLine:i%2 != 0}">
            <el-tooltip class="item" effect="dark" :content="item.caseNo" placement="top" popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column">{{item.caseNo}}</div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="item.claimDept" placement="top" popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column">{{item.claimDept}}</div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="item.claimTime" placement="top" popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column">{{item.claimTime}}</div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="item.claim == '1'?'处置中':'已完成'" placement="top"
                        popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column"
                   :class="{blueColor:item.handleStatus == '3',yellowColor:item.handleStatus == '2'}">{{item.handleStatus == "2"?"处置中":"已完成"}}</div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <!--回访跟踪-->
    <div class="jbgz-container" v-show="currentTab == 0">
      <div class="jbgz-container-tabs">
        <div class="jbgz-container-tab" v-for="(item,i) in hfgzTabList" :key="i"
             :class="{jbgzContainerActiveTab: currenthfgz == i}" @click="hfgzTabClick(i,item)">
          {{ item.label }}
        </div>
      </div>
      <div class="jbgz-container-table">
        <div class="jbgz-container-table-container-line lineTitle">
          <div class="jbgz-container-table-container-line-column columnTitle">案件编号</div>
          <div class="jbgz-container-table-container-line-column columnTitle">区域</div>
          <div class="jbgz-container-table-container-line-column columnTitle">决定送达日期</div>
          <div class="jbgz-container-table-container-line-column columnTitle">回访状态</div>
        </div>
        <div class="jbgz-container-table-container">
          <div class="jbgz-container-table-container-line" v-for="(item,i) in tableData2" :key="i"
               :class="{twiceLine:i%2 != 0}" @click="showDetail(item.caseNo)">
            <div class="jbgz-container-table-container-line-column" :title="item.caseNo">{{item.caseNo}}</div>
            <div class="jbgz-container-table-container-line-column">{{item.nationRegionName}}</div>
            <div class="jbgz-container-table-container-line-column">{{item.deciDeliveryDate}}</div>
            <div class="jbgz-container-table-container-line-column"
                 :class="{blueColor:item.followStatus == '2',yellowColor:item.followStatus == '3'}">{{labelGet(followStatusList,item.followStatus)}}</div>
          </div>
        </div>
      </div>
    </div>

    <caseDetailTableDialog :case-no='caseNo' :visible='showAjDetailTableDialog' @close='showAjDetailTableDialog = false'></caseDetailTableDialog>
  </div>
</template>

<script>
import moment from 'moment'
import { gethfgzList, getjbgzList } from '@/api/ajhf'
import { registerCityYearChangeEvents } from '@/utils'
import caseDetailTableDialog from '@/pages/ajhf/left/hfgk/caseDetailTableDialog'
export default {
  name: 'index',
  components: {
    caseDetailTableDialog
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      value2: [
        new Date().getFullYear() + "-01-01",
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      currentTab: 0,
      Tabs: ["回访跟踪","认领跟踪"],
      currentjbgz: 0,
      currenthfgz: 0,
      jbgzTabList: [
        {
          type: "全部",
          value: ""
        },
        {
          type: "处置中",
          value: "2"
        },
        {
          type: "已完成",
          value: "3"
        }
      ],
      hfgzTabList: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '未回访',
          value: '1',
        },
        {
          label: '回访中',
          value: '3',
        },
        {
          label: '已回访',
          value: '2',
        }
      ],
      tableData: [],
      tableData2: [],
      // 回访状态列表
      followStatusList: [
        {
          label: '无需回访',
          value: '0',
        },
        {
          label: '未回访',
          value: '1',
        },
        {
          label: '回访中',
          value: '3',
        },
        {
          label: '已回访',
          value: '2',
        }
      ],

      caseNo:"",
      showAjDetailTableDialog: false
    }
  },
  computed: {},
  mounted() {
    registerCityYearChangeEvents(this,this.initApi)
  },
  methods: {
    initApi(city,year) {
      this.getJbgz(this.value2, city, this.jbgzTabList[this.currentjbgz].value)
      this.getHfgz(this.value2, city, this.hfgzTabList[this.currenthfgz].value)
    },
    dateChange(range) {
      this.getJbgz(range, this.city, this.jbgzTabList[this.currentjbgz].value)
      this.getHfgz(range, this.city, this.hfgzTabList[this.currenthfgz].value)
    },
    jbgzTabClick(i, item) {
      this.currentjbgz = i
      this.getJbgz(this.value2, this.city, item.value)
    },
    hfgzTabClick(i, item) {
      this.currenthfgz = i
      this.getHfgz(this.value2, this.city, item.value)
    },
    //获取交办跟踪列表
    getJbgz(range, city, value) {
      getjbgzList({
        xsq: city,
        startTime: range[0],
        endTime: range[1],
        handleStatus:value
      }).then(res => {
        if (res.code == 200) {
          let resdata = res.data
          this.tableData = resdata
        }
      })
    },
    //获取回访跟踪列表
    getHfgz(range, city, value) {
      gethfgzList({
        nationRegionName: city === "金华市"?"":city,
        deciDeliveryDate: range[0],
        deciDeliveryDateEnd: range[1],
        followStatus:value
      }).then(res => {
        this.tableData2 = res.data
      })
    },
    // 字典翻译
    labelGet(list, value) {
      if (!Array.isArray(list)) {
        console.error("Expected an array for the 'list' parameter.")
        return ''
      }

      const foundItem = list.find((item) => item.value == value)

      // 确保找到对应的item后再返回其label属性，否则返回空字符串
      return value === null || value === undefined || value === '' || !foundItem
        ? ''
        : foundItem.label
    },
    showDetail(caseNO) {
      this.caseNo = caseNO
      this.showAjDetailTableDialog = true
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.commonTab {
  font-size: 45px !important;
}

.yearBox {
  /deep/ .yearChange {
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
    }
    .el-picker-panel {
      left: -300px !important;
    }
  }
}

.jbgz-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.jbgz-container-tabs {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 27px;
}

.jbgz-container-tab {
  width: 210.3px;
  height: 59px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 36px;
  color: #ABCEEF;
  font-style: italic;
  text-align: center;
  cursor: pointer;
}

.jbgzContainerActiveTab {
  width: 210.3px;
  height: 59px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 36px;
  color: #DAEDFF;
  font-style: italic;
  background: url('@/assets/ajhf/activeBg.png') no-repeat;
  background-size: cover;
  text-align: center;
}

.jbgz-container-table {
  width: 976px;
  margin-top: 37px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.jbgz-container-table-container {
  width: 100%;
  height: 700px;
  overflow-y: scroll;
}

.jbgz-container-table-container-line {
  width: 100%;
  height: 100px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background: rgba(50, 134, 248, 0.25);
}

.lineTitle {
  background: transparent !important;
}

.twiceLine {
  background: rgba(50, 134, 248, 0.15) !important;
}

.columnTitle {
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-size: 30px;
}

.jbgz-container-table-container-line-column {
  flex: 1;
  height: 100px;
  line-height: 100px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 30px;
  color: #FFFFFF;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 30px;
}
.blueColor {
  color: #3CFDFF !important;
}

.yellowColor {
  color: #EED252 !important;
}

.tipFont {
  font-size: 32px;
}

</style>