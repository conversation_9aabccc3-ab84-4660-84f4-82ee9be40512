<!--执法仪弹窗国标-->
<template>
  <ygfDialog :visible='visible' width='1500px' style='margin-top: 240px;'>
    <div class="zfry-video-container">
      <div class="rwgz-tc">
        <div class="rw-title flex-between">
          <div class="fs-44 font-syBold dialogTitle">{{ title }}</div>
          <div class="close" @click="closeVideo"></div>
        </div>
        <video
          ref="video"
          controls="controls"
          autoplay="autoplay"
          muted="false"
          style="
            width: 94%;
            height: 90%;
            position: absolute;
            top: 75px;
            left: 50px;
          "
        ></video>
        <div
          style="
            z-index: 1000;
            position: absolute;
            top: 120px;
            left: 60px;
            cursor: pointer;
            display: none;
          "
        >
          <button
            style="width: 70px; height: 30px; margin-bottom: 10px"
            @click="startAudio"
          >
            呼叫
          </button>
        </div>
      </div>
    </div>
  </ygfDialog>
</template>

<script>
import ygfDialog from '@/components/ygfDialog'
import { hex_md5 } from './md5.js'

export default {
  name: "index",
  components: {
    ygfDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    baseURL: {
      type: String,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    pwd: {
      type: String,
      required: true
    },
    videoCode: {
      type: String,
      default: "T0C2675"
    },
    title: {
      type: String,
      default: "执法记录仪"
    },
    type: {
      type: String,
      default: "openVideoTest" // 可选值: openVideoTest, ddhjVideoTest
    }
  },
  data() {
    return {
      player: null,
      isAudio: false,
      num: 0,
      recorders: null,
      recordersPlatform: null,
      ws: null,
      timerId: null,
      dataInfo_set: [],
      form: {
        hostbodyArr: null,
        hostbody: null
      },
      dataInfo_hostbody: null,
      zfjlyApi: null
    };
  },
  created() {
    this.initZfjlyApi();
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    if (this.timerId) {
      clearInterval(this.timerId);
    }
    if (this.player) {
      this.player = null;
    }
  },
  methods: {
    async init() {
      if (this.type === "openVideoTest") {
        await this.loginZFJLY();
        setTimeout(() => {
          this.getInfo();
        }, 2000);
      } else if (this.type === "ddhjVideoTest") {
        await this.loginZFJLY();
        setTimeout(() => {
          this.getInfo();
          this.startAudio();
        }, 2000);
      }
    },
    initZfjlyApi() {
      const axios = require('axios');

      this.zfjlyApi = {
        get: axios.create({
          baseURL: this.baseURL,
          withCredentials: true
        }).get,
        post: axios.create({
          baseURL: this.baseURL,
          withCredentials: true
        }).post,

        hex_md5(str) {
          return hex_md5(str);
        },

        param_up(param_arr) {
          var keys = Object.keys(param_arr).sort();
          var string = "";
          for (var i = 0; i < keys.length; i++) {
            var k = keys[i];
            string += k + "=" + param_arr[k] + ";";
          }
          string += this.hex_md5("Pe2695jingyi");
          let str_encode = encodeURIComponent(string);
          param_arr.pe_signals = this.hex_md5(str_encode);
          return JSON.stringify(param_arr);
        },

        extendSignal(target) {
          let keys = Object.keys(target),
            arr = [],
            solt = "Pe2695jingyi",
            str,
            pe_signals;
          keys.sort();
          keys.forEach((key) => {
            const value = JSON.stringify(target[key]);
            arr.push(`${key}=${value}`);
          });
          str = arr.join(";") + this.hex_md5(solt);
          str = encodeURIComponent(str);
          pe_signals = this.hex_md5(str);
          target.pe_signals = pe_signals;
          return target;
        }
      };
    },
    closeVideo() {
      this.$emit('close');
    },
    async loginZFJLY() {
      try {
        const res = await this.zfjlyApi.get(`rest/index/login/get?key=""`);

        const send = {
          username: this.username,
          password: this.zfjlyApi.hex_md5(this.pwd)
        };

        const el = await this.login(send);

        if (el.data.code === 200) {
          await this.heartbeat();
          const e = await this.getUserInformation();

          try {
            this.ws = new WebSocket(e.data.data.wsurl);
          } catch (error) {
            console.log("WebSocket连接错误:", error);
          }

          const data1 = {
            logincode: e.data.data.logincode,
            username: e.data.data.username,
            scode: e.data.data.scode,
            cate: e.data.data.auth_cate
          };

          const psd = {
            command: "client_login",
            data: JSON.stringify(data1)
          };

          this.ws.onopen = () => {
            this.ws.send(JSON.stringify(psd));
          };

          this.ws.onerror = (e) => {
            console.warn("socket连接出错", e);
            this.ws.close();
            this.ws = null;
          };

          this.ws.onclose = () => {
            console.log("ws已断开连接");
          };

          this.ws.onmessage = (event) => {
            console.log("收到消息:", event.data);
          };
        }
      } catch (error) {
        console.error("登录执法记录仪平台失败:", error);
      }
    },
    login(param) {
      let { username, password } = param;
      let login_if = `{"username":"${username}","password":"${password}","key":""}`;
      let data = this.zfjlyApi.param_up({
        login_info: login_if
      });

      return this.zfjlyApi.post('/rest/index/login/login', data);
    },
    getUserInformation() {
      return this.zfjlyApi.get('/rest/user/user/get_info');
    },
    heartbeat() {
      return new Promise((resolve) => {
        this.heart();
        this.timerId = setInterval(this.heart, 20000);
        resolve();
      });
    },
    heart() {
      this.online().then((e) => {
        console.log("心跳请求成功");
      });
    },
    online() {
      return this.zfjlyApi.get('rest/other/user/online');
    },
    async getDeviceInfo() {
      try {
        const res = await this.unitEquipTreeGB("1001", "", [1, 2, 3, 4, 5]);
        var lineon = [];
        res.data.data.gblineon.forEach((item) => {
          lineon.push(item.hostbody);
        });
        this.pulldata = res.data.gblineon;
        this.dataInfo_hostbody = lineon.toString();
        this.form.hostbodyArr = this.dataInfo_hostbody;
      } catch (error) {
        console.error("获取设备信息失败:", error);
      }
    },
    unitEquipTreeGB(unit = '', key = '', recorder_type_arr = []) {
      let data = { unit, key, recorder_type_arr };
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('rest/monitor/videopatrols/getdevice', data);
    },
    async getInfo(vdDept = [this.videoCode]) {
      try {
        await this.getDeviceInfo();
        const send = {
          hostbody_arr: vdDept
        };

        const res = await this.startLiveVideo(send);

        if (res.data.code == 200) {
          const ws = `ws://${res.data.data[0].wsip}:${res.data.data[0].wsport}/RTSP/AAC`;
          const rtsp = res.data.data[0].rtsp;
          const logincode = res.data.data[0].logincode;

          console.log("WebSocket和RTSP地址:", ws, rtsp);

          // 导入播放器模块
          import('./JY-chromePlayer.min.js').then(({ Wsplayer }) => {
            this.player = new Wsplayer(ws, rtsp, this.$refs.video, "isGB");
            this.player.openws().then((obj) => {
              if (obj.type == true) {
                console.log("视频流打开成功");
                this.$refs.video.setAttribute("class", "chorme");
              } else {
                this.$message({
                  message: "拉流失败",
                  type: "warning"
                });
                this.closeEnd();
              }
            });
          });
        } else {
          this.$message({
            message: "设备不在线",
            type: "warning"
          });
        }
      } catch (error) {
        console.error("获取视频流失败:", error);
      }
    },
    startLiveVideo(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/startLive', data);
    },
    startLiveAudio(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/startAudio', data);
    },
    async startAudio(vdDept = [this.videoCode]) {
      try {
        const res = await this.unitEquipTreeGB("1001", "", [1, 2, 3, 4, 5]);
        let imei;

        res.data.data.gblineon.forEach((item) => {
          if (item.hostbody === this.form.hostbody) {
            imei = item.imei;
          }
        });

        if (this.isAudio) {
          const send = {
            imei: imei,
            type: "startmute"
          };

          const res = await this.send_cmd(send);
          this.isAudio = false;
        } else {
          if (this.num === 0) {
            await this.setupAudio();
            await this.initializeAudioStream(vdDept, imei);
          } else {
            const send = {
              imei: imei,
              type: "stopmute"
            };

            const res = await this.send_cmd(send);
            this.isAudio = true;
          }
        }
      } catch (error) {
        console.error("音频通话失败:", error);
      }
    },
    setupAudio() {
      return new Promise((resolve, reject) => {
        import('./JY-chromePlayer.min.js').then(({ HZRecorder_pcm_push, HZRecorder_pcm }) => {
          navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia;

          if (navigator.getUserMedia) {
            navigator.getUserMedia(
              { audio: true },
              (stream) => {
                this.recordersPlatform = new HZRecorder_pcm_push(stream, {});
                resolve();
              },
              (error) => {
                console.error("无法获取音频设备:", error);
                reject(error);
              }
            );
          } else {
            const error = new Error("当前浏览器不支持录音功能");
            console.error(error.message);
            reject(error);
          }
        });
      });
    },
    async initializeAudioStream(vdDept, imei) {
      return new Promise((resolve, reject) => {
        navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia;

        if (!navigator.getUserMedia) {
          this.$message({
            message: "浏览器不支持音频输入",
            type: "warning"
          });
          reject(new Error("浏览器不支持音频输入"));
          return;
        }

        navigator.getUserMedia(
          { audio: true },
          async (mediaStream) => {
            import('./JY-chromePlayer.min.js').then(async ({ HZRecorder_pcm }) => {
              this.recorders = new HZRecorder_pcm(mediaStream, {});

              try {
                const send = {
                  hostbody_arr: vdDept
                };

                const res = await this.startLiveAudio(send);

                if (res.data.code === 200) {
                  const wsBroadcast = `ws://${res.data.data[0].wsip}:${res.data.data[0].wsport}/RTSP/AAC/Broadcast`;
                  const logincode = res.data.data[0].logincode;

                  this.isAudio = true;
                  this.num++;

                  this.recordersPlatform.openWebSocket(wsBroadcast, logincode)
                    .then(() => {
                      console.log("音频WebSocket连接成功");
                      resolve();
                    })
                    .catch((error) => {
                      console.error("音频WebSocket连接失败:", error);
                      reject(error);
                    });
                } else {
                  this.$message({
                    message: "设备不在线",
                    type: "warning"
                  });
                  reject(new Error("设备不在线"));
                }
              } catch (error) {
                console.error("初始化音频流失败:", error);
                reject(error);
              }
            });
          },
          (error) => {
            console.error("获取音频设备失败:", error);
            reject(error);
          }
        );
      });
    },
    send_cmd(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/gis/gismoni/send_cmd', data);
    },
    closeEnd() {
      if (!this.isAudio) {
        const send = {
          hostbody_arr: [this.form.hostbody],
          sn_arr: [this.sn]
        };
        this.stopLive(send).then((res) => {
          if (res.code == 200) {
            this.player.stop();
            this.$refs.video.setAttribute("class", "chorme disappear");
          }
        });
      } else {
        const send1 = {
          hostbody_arr: [this.form.hostbody],
          wsChannelId_arr: [this.wsChannelId]
        };
        this.stopAudio(send1).then((res) => {
          this.isAudio = false;
          this.recorders.closeWebsocket();
          const sent = {
            hostbody_arr: [this.form.hostbody],
            sn_arr: [this.sn]
          };
          this.stopLive(sent).then((res) => {
            if (res.code == 200) {
              this.player.stop();
              this.$refs.video.setAttribute("class", "chorme disappear");
            }
          });
        });
      }
    },
    stopLive(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/stopLive', data);
    },
    stopAudio(data) {
      this.zfjlyApi.extendSignal(data);
      return this.zfjlyApi.post('/rest/live/chrome/stopAudio', data);
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initZfjlyApi();
        this.init();
      }
    }
  }
};
</script>

<style scoped>
.zfry-video-container {
  width: 100%;
  height: 100%;
}

.rwgz-tc {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url("@/assets/zhdd/bg_panel.png");
  background-size: 100% 100%;
  border-radius: 57px;
}

.rw-title {
  position: absolute;
  top: 50px;
  z-index: 888;
  border-radius: 57px 57px 0 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  padding: 1% 3%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close {
  background: url("@/assets/zhdd/close.png") no-repeat;
  width: 34px;
  height: 34px;
  cursor: pointer;
}
</style>