<template>
  <div>
    <div class="table">
      <div class="table-header">
        <div class="th" v-for="item of thConfig" :key="item.field" :style="{ width: item.width }">{{ item.th }}</div>
      </div>
      <div class="table-body">
        <div
          class="tr"
          v-for="(item, index) of tableData"
          :class="{ 'tr-bg': index % 2 === 1 }"
          :key="item.id"
          @click="infoClick(item)"
        >
          <div
            v-for="(config, idx) in thConfig"
            :key="idx"
            class="td"
            :class="{ cursor: idx === 3 }"
            :style="{
              minWidth: config.width,
              color: idx === 3 ? item.color : undefined
            }"
            v-html="item[config.field]"
            :title="config.hover ? item[config.field] : ''"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    thConfig: {
      type: Array,
      default: () => [],
      required: true
    },
    tableData: {
      type: Array,
      default: () => [],
      required: true
    },
    tableHeight: {
      type: [String, Number],
      default: 375
    }
  },
  data() {
    return {}
  },
  methods: {
    infoClick(item) {
      this.$emit('infoClick', item)
    },
  },
}
</script>

<style lang="less" scoped>
.table {
  .table-header {
    display: flex;
    width: 100%;
    height: 92px;
    line-height: 92px;
    background: rgba(91, 180, 255, 0.2);
    background-size: 100% 100%;
    .th {
      width: 25%;
      color: #CDE7FF;
      font-size: 32px;
      text-align: center;
      box-sizing: border-box;
      padding: 0 24px;
    }
  }
  .table-body {
    overflow-y: auto;
    height: v-bind('typeof tableHeight === "number" ? tableHeight + "px" : tableHeight');
    .tr {
      display: flex;
      width: 100%;
      height: 92px;
      &:hover {
        background: rgba(91, 180, 255, 0.3);
      }
      .td {
        width: 25%;
        box-sizing: border-box;
        height: 68px;
        line-height: 92px;
        padding: 0 24px;
        text-align: center;
        font-size: 28px;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.cursor {
          cursor: pointer;
        }
      }
    }
    .tr-bg {
      background: rgba(91, 180, 255, 0.2);
    }
  }
}
</style>
