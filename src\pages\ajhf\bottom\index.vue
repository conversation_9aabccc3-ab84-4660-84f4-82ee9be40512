<template>
  <div class='bottom'>
    <div class="bottom-container" v-cloak>
      <CommonTitle text='回访排名'></CommonTitle>
      <div style="position: absolute; top: 26px; left: 1430px; z-index: 2" v-show="year == $currentYear" class='yearChange'>
        <el-date-picker
          v-model="value"
          type="monthrange"
          @change="(range) => initApi(range,city,year)"
          range-separator="-"
          start-placeholder="开始月份"
          value-format="yyyy-MM"
          end-placeholder="结束月份"
          :append-to-body='false'>
        </el-date-picker>
      </div>
      <div class="chart" id="ajhfBottomChart"></div>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { gethfpmList } from '@/api/ajhf'
export default {
  name: 'index',
  components: {
    CommonTitle
  },
  data() {
    return {
      year: localStorage.getItem("year"),
      city: localStorage.getItem("city"),
      value: [
        new Date().getFullYear() + "-01",
        new Date().getFullYear() +
        "-" +
        (new Date().getMonth() + 1).toString().padStart(2, "0"),
      ]
    }
  },
  computed: {},
  mounted() {
    this.$bus.$on('cityChange', (city) => {
      this.city = city;
      this.initApi(this.value, city,localStorage.getItem("year"));
    })
    this.$bus.$on('yearChange', (year) => {
      this.year = year;
      this.value = this.$getYearList(year);
      this.initApi(this.value,localStorage.getItem("city"),year);
    })
    this.initApi(this.value,localStorage.getItem("city"),localStorage.getItem("year"));
  },
  methods: {
    initApi(range, city, year) {
      gethfpmList({
        xsq: city,
        startTime: range[0],
        endTime: range[1],
      }).then(res => {
        if (res.code == 200) {
          this.chartsData = res.data
          setTimeout(() => {
            this.initChart();
          },300)
        }
      })
    },
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("ajhfBottomChart"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: '{b}: <br/> {c}%',
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          itemGap: 45,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
        },
        grid: {
          left: "2%",
          right: "2%",
          top: "10%",
          bottom: "12%",
          containLabel: true,
        },
        xAxis: [{
          type: "category",
          data: this.chartsData.map(item => item.xsq),
          axisLine: {
            lineStyle: {
              color: "rgb(119,179,241,.4)", // 颜色
              width: 1, // 粗细
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 28,
            },
          },
        }, ],
        yAxis: [{
          name: "",
          type: "value",
          nameTextStyle: {
            fontSize: 24,
            color: "#D6E7F9",
            padding: 5,
          },
          splitLine: {
            lineStyle: {
              color: "rgb(119,179,241,.4)",
            },
          },
          axisLabel: {
            textStyle: {
              fontSize: 28,
              color: "#D6E7F9",
            },
          },
        }],
        series: [{
          name: "",
          type: "bar",
          barWidth: "20%",
          itemStyle: {
            normal: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: "#28F7E1",
              },
                {
                  offset: 1,
                  color: "#1A80FF",
                },
              ]),
              barBorderRadius: 4,
            },
          },
          label: {
            show: true,
            position: 'top',
            color: "#fff",
            formatter: function (params) {
              return params.value + "%";
            },
            fontSize: 32,
            textStyle: {
              color: '#FFFFFF',
              fontWeight: 'bold',
              fontFamily: 'Source Han Sans CN'
            }
          },
          data: this.chartsData.map(item => item.pm)
        }],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.bottom-container {
  width: 1760px;
  height: 525px;
  padding: 20px;
  box-sizing: border-box;
  background: url("@/assets/index/bottom-bg.png") no-repeat;
  background-size: 100% 100%;
  /deep/ .yearChange {
    .el-input__inner {
      height: 48px !important;
      background-color: #132c4e !important;
      border: 2px solid #afdcfb !important;
      color: #fff !important;
      border-radius: 15px !important;
    }
  }
}

.chart {
  width: 100%;
  height: 465px;
}
</style>